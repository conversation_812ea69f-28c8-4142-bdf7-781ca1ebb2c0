<?php

namespace App\Http\Requests\Twill;

use A17\Twill\Http\Requests\Admin\Request;
use App\Http\Requests\Twill\ValidationTraits\SeoValidation;

class ProductRangeRequest extends Request
{
	use \App\Http\Requests\Twill\ValidationTraits\SeoValidation;

    public function rulesForCreate()
    {
        return [];
    }

    public function rulesForUpdate()
    {
        $id = $this->route('productRange');
		$seo_metas_id = \App\Models\ProductRange::where('id', $id)->pluck('seo_meta_id')->toArray();

        return [
            'seo_meta-page_title' => [
                'nullable', 
                'max:65', 
                'unique:seo_metas,page_title,' . $seo_metas_id[0]
            ],
            'seo_meta-page_description' => [
                'nullable', 
                'max:160', 
                'unique:seo_metas,page_description,' . $seo_metas_id[0]
            ],
            'seo_meta-page_keywords' => [
                'nullable',
                'max:160', 
                'unique:seo_metas,page_keywords,' . $seo_metas_id[0]
            ],
        ];
    }
}
