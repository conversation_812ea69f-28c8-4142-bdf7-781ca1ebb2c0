<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleBlocks;
use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleFiles;
use A17\Twill\Repositories\Behaviors\HandleRevisions;
use A17\Twill\Repositories\Behaviors\HandleTags;
use App\Repositories\GlobalRepository;
use App\Models\Condition;
use function GuzzleHttp\json_encode;

class ConditionRepository extends GlobalRepository
{
    use HandleBlocks, HandleSlugs, HandleMedias, HandleFiles, HandleRevisions, HandleTags;

    public function __construct(Condition $model)
    {
        $this->model = $model;
	}
	
	public function getFormFields(\A17\Twill\Models\Contracts\TwillModelContract $object): array
    {
		$fields = parent::getFormFields($object);

		//related treatments
		$fields['browsers']['treatments'] = $this->getFormFieldsForBrowser($object, 'treatments', 'content');

		//Q and A repeater
		if (isset($fields['conditions_questions_and_answers']) && !empty($fields['conditions_questions_and_answers'])) {
			$fields = $this->getJsonRepeater($fields, 'question_and_answer', 'conditions_questions_and_answers');
		}

		//Selectcast Repeater
		if (isset($fields['selectcast_embeds']) && !empty($fields['selectcast_embeds'])) {
			$fields = $this->getJsonRepeater($fields, 'selectcast_embed', 'selectcast_embeds');
		}

        return $fields;
	}

	public function prepareFieldsBeforeSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): array
    {
        if (isset($fields['repeaters']['question_and_answer'])) {
			$fields['conditions_questions_and_answers'] = $fields['repeaters']['question_and_answer'];
		}

		if (isset($fields['repeaters']['selectcast_embed'])) {
			$fields['selectcast_embeds'] = $fields['repeaters']['selectcast_embed'];
		}

        return parent::prepareFieldsBeforeSave($object, $fields);
	}
	
	public function afterSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): void {
		$this->updateBrowser($object, $fields, 'treatments');
		$this->updateMultiSelect($object, $fields, 'package_categories');
		parent::afterSave($object, $fields);
	}

	// implement the filter method
	public function filter(\Illuminate\Database\Eloquent\Builder $query, array $scopes = []): \Illuminate\Database\Eloquent\Builder {

		if (isset($scopes['category'])) {
			$query->whereHas('category', function ($query) use ($scopes) {
				$query->where('id', $scopes['category']);
			});
			unset($scopes['category']);
		}

		return parent::filter($query, $scopes);
	}
}
