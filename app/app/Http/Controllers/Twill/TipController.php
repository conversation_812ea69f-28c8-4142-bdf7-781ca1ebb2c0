<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;

class TipController extends ModuleController
{
	protected $moduleName = 'tips';

	//protected $permalinkBase = '';

	protected $indexColumns = [
        'title' => [ // field column
            'title' => 'Title',
            'field' => 'title',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
		'slug' => [
			'title' => 'Slug',
			'field' => 'slug',
			'sort' => true, // column is sortable
			'visible' => true, // will be available from the columns settings dropdown
		],
	];

	// public function __construct()
	// {
	// 	$this->permalinkBase = config('renewal.general.tips_prefix') . '-tips';
	// }
	
	/*
     * Can be used in child classes to disable the content editor (full screen block editor)
     */
	protected $disableEditor = true;

	protected $previewView = 'frontend.tips.detail';

	protected function previewData($item)
	{
		$tips = \App\Models\Tip::all();

		return [
			'content' => $item,
			'tips'    => $tips
		];
	}

	public function getPermalinkBaseUrl()
    {
		$this->permalinkBase = config('renewal.general.tips_prefix') . '-tips';
		
        return request()->getScheme() . '://' . config('app.url') . '/'
            . ($this->moduleHas('translations') ? '{language}/' : '')
            . ($this->moduleHas('revisions') ? '{preview}/' : '')
            . ($this->permalinkBase ?? $this->moduleName)
            . (isset($this->permalinkBase) && empty($this->permalinkBase) ? '' : '/');
    }
}
