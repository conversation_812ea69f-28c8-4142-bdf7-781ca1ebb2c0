<?php

namespace App\Http\Requests\Twill;

use A17\Twill\Http\Requests\Admin\Request;
use App\Http\Requests\Twill\ValidationTraits\SeoValidation;

class MooimaakRequest extends Request
{
    use \App\Http\Requests\Twill\ValidationTraits\SeoValidation;
    public function rulesForCreate()
    {
        return [];
    }

    public function rulesForUpdate()
    {
        $id = $this->route('mooimaak');
        $seo_metas_id = \App\Models\Mooimaak::where('id', $id)->pluck('seo_meta_id')->toArray();

        $rules = $this->get_seo_rules($seo_metas_id);

        return $rules;
    }
}
