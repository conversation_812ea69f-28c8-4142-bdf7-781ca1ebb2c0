<?php

namespace App\Models;

use A17\Twill\Models\Behaviors\HasBlocks;
use A17\Twill\Models\Behaviors\HasSlug;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasFiles;
use A17\Twill\Models\Behaviors\HasRevisions;
use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Behaviors\Sortable;
use App\Models\Behaviors\HasSeo;
use A17\Twill\Models\Model;
use Laravel\Scout\Searchable;

class StaffMember extends Model implements Sortable
{
    use HasBlocks, HasSlug, HasMedias, HasFiles, HasRevisions, HasPosition, HasSeo, Searchable, HasPosition;

    protected $fillable = [
        'published',
        'title',
        'description',
        'position',
        'qualification',
		'position_at_company',
		'thumbnail_image',
		'main_image',
		'staff_members_type_id',
        'expert',
        'publish_start_date',
        'publish_end_date',
        'excerpt',
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['type'];
    
    // uncomment and modify this as needed if you use the HasSlug trait
    public $slugAttributes = [
        'title',
    ];

    // uncomment and modify this as needed if you use the HasMedias trait
    public $mediasParams = [
        'cover' => [
            'default' => [
                [
                    'name' => 'square',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 1,
				]
            ],
            'mobile' => [
                [
                    'name' => 'mobile',
                    'ratio' => 1,
                ],
            ],
        ],
    ];
	
	public function type() {
		return $this->belongsTo(\App\Models\StaffMemberType::class, 'staff_members_type_id');
	}

	public function shouldBeSearchable()
	{
		return $this->published;
	}

	/**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
		$fullArray = $this->toArray();
		
		$fullArray = $this->transform($fullArray);

		// Customize array...
		return [
			'title' => $fullArray['title'],
			'type'  => $this->type->title ?? ''
		];
    }
}
