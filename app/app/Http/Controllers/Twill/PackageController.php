<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;

class PackageController extends ModuleController
{
    protected $moduleName = 'packages';

    protected $previewView = 'frontend.packageCategories.detail';

    /*
     * Options of the index view
     */
    protected $indexOptions = [
        'reorder' => true,
        'permalink' => false,
        'feature' => true,
    ];

    /*
     * Can be used in child classes to disable the content editor (full screen block editor)
     */
    protected $disableEditor = true;

    protected $indexColumns = [
        'title' => [ // field column
            'title' => 'Title',
            'field' => 'title',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'slug' => [
            'title' => 'Slug',
            'field' => 'slug',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'category' => [ // relation column
            'title' => 'Category',
            'sort' => true,
            'relationship' => 'category',
            'field' => 'title'
        ],
    ];

    /*
     * Filters mapping ('filterName' => 'filterColumn')
     * You can associate items list to filters by having a filterNameList key in the indexData array
     * For example, 'category' => 'category_id' and 'categoryList' => app(CategoryRepository::class)->listAll()
     */
    protected $filters = [
        'category' => 'category'
    ];

    protected function indexData($request)
    {
        $categoryList = \App\Models\PackageCategory::all()->map(function ($item) {
            return [
                'value' => $item->id,
                'label' => $item->title
            ];
        })->toArray();

        return [
            'categoryList' => $categoryList
        ];
    }

    /*
     * Add anything you would like to have available in your module's form view
     * For example, relationship lists for multiselect form fields
     */
    protected function formData($request)
    {
        //$itemId = $request->route()->parameters['priceCategory'];
        $categories = [];
        $categories_list = \App\Models\PackageCategory::all();
        foreach ($categories_list as $category) {
            $categories[] = [
                'value' => $category->id,
                'label' => $category->title
            ];
        }
        return ['categories' => $categories];
    }

    protected function previewData($item)
    {
        $package_categories = \App\Models\PackageCategory::all();
        $content = $item->category;

        return [
            'content' => $content,
            'package_categories' => $package_categories
        ];
    }
}
