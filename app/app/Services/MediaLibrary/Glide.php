<?php
namespace App\Services\MediaLibrary;
use A17\Twill\Services\MediaLibrary\ImageServiceInterface;
use A17\Twill\Services\MediaLibrary\ImageServiceDefaults;

class Glide implements ImageServiceInterface
{
    use ImageServiceDefaults;
	
	public function getUrl($id, array $params = [])
    {
        return $this->getRawUrl($id);
	}
	
    public function getUrlWithCrop($id, array $crop_params, array $params = [])
    {
        return $this->getRawUrl($id);
	}
	
    public function getUrlWithFocalCrop($id, array $cropParams, $width, $height, array $params = [])
    {
        return $this->getRawUrl($id);
	}
	
    public function getLQIPUrl($id, array $params = [])
    {
        return $this->getRawUrl($id);
	}
	
    public function getSocialUrl($id, array $params = [])
    {
        return $this->getRawUrl($id);
	}
	
    public function getCmsUrl($id, array $params = [])
    {
        return $this->getRawUrl($id);
	}
	
    public function getRawUrl($id)
    {
        return '/' . $id;
	}
	
    public function getDimensions($id)
    {
        return null;
	}
	
    protected function getCrop($crop_params)
    {
        if (!empty($crop_params)) {
            return ['crop' => $crop_params['crop_w'] . ',' . $crop_params['crop_h']] . ',' . $crop_params['crop_x'] . ',' . $crop_params['crop_y'];
        }
        return [];
    }
}