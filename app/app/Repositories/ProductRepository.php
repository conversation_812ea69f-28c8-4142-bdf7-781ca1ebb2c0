<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleBlocks;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleFiles;
use A17\Twill\Repositories\Behaviors\HandleRevisions;
use A17\Twill\Repositories\ModuleRepository;
use App\Models\Product;

class ProductRepository extends ModuleRepository
{
    use HandleBlocks, HandleMedias, HandleFiles, HandleRevisions;

    public function __construct(Product $model)
    {
        $this->model = $model;
	}
	
	// implement the filter method
	public function filter(\Illuminate\Database\Eloquent\Builder $query, array $scopes = []): \Illuminate\Database\Eloquent\Builder {

		if (isset($scopes['range'])) {
			$query->whereHas('range', function ($query) use ($scopes) {
				$query->where('id', $scopes['range']);
			});
			unset($scopes['range']);
		}

		return parent::filter($query, $scopes);
	}
}
