<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\View;
use A17\Twill\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Relations\Relation;
use A17\Twill\Models\Feature;
use \App\Models\Branch;
use \App\Search\Sitewide;
use App\Services\ABTestingService;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\App;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register() {}

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);

        Relation::morphMap([
            'conditions' => 'App\Models\Condition',
        ]);

        Relation::morphMap([
            'treatments' => 'App\Models\Treatment',
        ]);

        // TODO: Add this back to re enable caching
        //Global settings available to all views (cached for 10 minutes)
        // $seconds = 60 * 10;
        // $global_settings = Cache::remember('global_settings', $seconds, function () {
        //     if (Schema::hasTable('users')) {
        //         $setting_sections = Setting::all()->groupBy('section');
        //         $global_settings = [];

        //         foreach ($setting_sections as $setting_section) {
        //             foreach ($setting_section as $setting) {
        //                 $global_settings[$setting['section']][$setting->key] = $setting;
        //             }
        //         }

        //         $global_settings['branch_list'] = Branch::select('id', 'title')->orderBy('title')->get();

        //         $global_settings['navigation'] = config('renewal.navigation');

        //         $global_settings['footer_navigation'] = config('renewal.footer-navigation');

        //         return $global_settings;
        //     }
        // });


        if (Schema::hasTable('users')) {
            $setting_sections = Setting::all()->groupBy('section');
            $global_settings = [];

            foreach ($setting_sections as $setting_section) {
                foreach ($setting_section as $setting) {
                    $global_settings[$setting['section']][$setting->key] = $setting;
                }
            }

            $global_settings['branch_list'] = Branch::select('id', 'title')->orderBy('title')->get();

            $global_settings['navigation'] = config('renewal.navigation');

            $global_settings['footer_navigation'] = config('renewal.footer-navigation');
        }


        View::share('global_settings', $global_settings);

        //Fix mix manifest not found issue on shared hosting
        // if (\App::environment('staging')) {
        // 	$this->app->bind('path.public', function() {
        // 		return base_path().'/../public_html';
        // 	});
        // }

        if (App::environment('production')) {
            $this->app->bind('path.public', function () {
                return config('renewal.general.pubic_path');
            });
        }

        //Agrigated search
        Sitewide::bootSearchable();

        // Paginator::useBootstrapFive();
        Paginator::useBootstrapFour();

        $this->app->singleton(ABTestingService::class);
    }
}
