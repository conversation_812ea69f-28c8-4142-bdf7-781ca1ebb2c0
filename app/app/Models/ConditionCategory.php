<?php

namespace App\Models;

use A17\Twill\Models\Model;
use Illuminate\Support\Facades\Cache;
use A17\Twill\Models\Behaviors\Sortable;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasPosition;

class ConditionCategory extends Model implements Sortable
{
    use HasPosition, HasMedias;

    protected $fillable = [
        'published',
        'title',
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
    ];

    public function conditions()
    {
        return $this->hasMany(\App\Models\Condition::class)->orderBy('title', 'ASC');
    }

    public $mediasParams = [
        'cover_image' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ],
            'mobile' => [
                [
                    'name' => 'mobile',
                    'ratio' => 1,
                ],
            ],
        ],
    ];

    public function sub_categories()
    {
        return $this->hasMany(ConditionSubCategory::class);
    }

    /**
     * Clear cache when data is saved or deleted.
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            Cache::forget('conditions_data');
        });

        static::deleted(function () {
            Cache::forget('conditions_data');
        });
    }
}
