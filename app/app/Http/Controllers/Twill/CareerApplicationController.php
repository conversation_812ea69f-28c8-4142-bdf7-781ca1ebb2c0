<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController as BaseModuleController;

class CareerApplicationController extends BaseModuleController
{
	/*
     * Can be used in child classes to disable the content editor (full screen block editor)
     */
    protected $disableEditor = true;

    protected $moduleName = 'careerApplications';

	/*
     * Options of the index view
     */
    protected $indexOptions = [
        'create' => false,
        'edit' => false,
        // 'publish' => false,
        // 'bulkPublish' => false,
        // 'feature' => false,
        // 'bulkFeature' => false,
        // 'restore' => true,
        // 'bulkRestore' => true,
        'delete' => true,
        // 'bulkDelete' => true,
        // 'reorder' => false,
        // 'permalink' => false,
        // 'bulkEdit' => false,
        // 'editInModal' => false,
	];
	
	/*
     * Available columns of the index view
     */
    protected $indexColumns = [
		'title' => [ // field column
            'title' => 'Name',
			'field' => 'title',
			'sort'  => true
        ],
        'email' => [
            'title' => 'Email',
            'field' => 'email',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
		],
		'cellphone' => [
            'title' => 'Cellphone',
            'field' => 'cellphone',
            'sort' => false, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
		'closest_branch' => [
            'title' => 'Closest Branch',
            'field' => 'closest_branch',
            'sort' => false, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
		'cv_url' => [
            'title' => 'CV',
            'field' => 'cv_url',
            'sort' => false, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'created_at' => [ // presenter column
            'title' => 'Date',
            'field' => 'date',
            'present' => true,
            'visible' => true,
        ]
	];
}
