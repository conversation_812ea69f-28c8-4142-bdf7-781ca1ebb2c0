<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\MinkiPost;
use App\Models\Mooimaak;
use App\Models\Page;

class MinkiPostController extends Controller
{
    public function index($season)
    {
        $listing = MinkiPost::where('season', $season)->published()->orderBy('publish_start_date', 'asc')->get();
        if ($listing->count() == 0) {
            return abort(404);
        }

        $content = Page::forSlug('minki-season-' . $season)->published()->visible()->first();

        return view('frontend.minki-posts.index', [
            'content' => $content,
            'listing' => $listing,
            'season' => $season,
            'seasons' => [6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
        ]);
    }

    public function detail($contestant)
    {
        $content = MinkiPost::forSlug($contestant)->published()->visible()->first();

        if (!$content) {
            return abort(404);
        }

        $listing = MinkiPost::where('season', $content->season)->published()->orderBy('publish_start_date', 'asc')->get();

        // get previous mooimaak
        // $previous = MinkiPost::where('id', '<', $content->id)
        //     ->where('season', $content->season)
        //     ->published()
        //     ->visible()
        //     ->orderBy('id','desc')
        //     ->first();

        // // get next mooimaak
        // $next = MinkiPost::where('id', '>', $content->id)
        //     ->where('season', $content->season)
        //     ->published()
        //     ->visible()
        //     ->orderBy('id','asc')
        //     ->first();

        return view('frontend.minki-posts.detail', [
            'content'   => $content,
            'listing' => $listing,
            'seasons' => [6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
        ]);
    }
}
