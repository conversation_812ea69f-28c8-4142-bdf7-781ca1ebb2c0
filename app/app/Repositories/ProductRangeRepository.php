<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleBlocks;
use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleFiles;
//use A17\Twill\Repositories\Behaviors\HandleRevisions;
use App\Repositories\GlobalRepository;
use App\Models\ProductRange;

class ProductRangeRepository extends GlobalRepository
{
    use HandleBlocks, HandleSlugs, HandleMedias, HandleFiles;

    public function __construct(ProductRange $model)
    {
        $this->model = $model;
    }
}
