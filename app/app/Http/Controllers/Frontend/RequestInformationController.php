<?php

namespace App\Http\Controllers\Frontend;

//use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\SaveAskDoctorForm;
use App\Mail\AdminNotifications;
use Illuminate\Support\Facades\Mail;
use App\Models\RequestInformation;
use A17\Twill\Repositories\SettingRepository;

class RequestInformationController extends Controller
{
    public function store(SaveAskDoctorForm $request) {
		$data = $request->validated();
		$data['title'] = $data['name'] . ' ' . $data['surname'];
		$data['published'] = true;

		$new_submission = RequestInformation::create($data);

		//Admin notification email
		$notification_email = app(SettingRepository::class)->byKey('notification_email', 'general');
		Mail::to($notification_email)->send(new AdminNotifications($new_submission));

		$redirect = $request->input('redirect') ?? '/';
		return redirect($redirect);
	}
}
