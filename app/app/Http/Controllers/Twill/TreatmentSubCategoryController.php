<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController as BaseModuleController;

class TreatmentSubCategoryController extends BaseModuleController
{
    protected $moduleName = 'treatmentSubCategories';

    protected $indexOptions = [
        'permalink' => false,
        'reorder' => true,
    ];

    /*
     * Add anything you would like to have available in your module's form view
     * For example, relationship lists for multiselect form fields
     */
    protected function formData($request)
    {
        $categories = [];
        $categories_list = \App\Models\TreatmentCategory::orderBy('position', 'ASC')->published()->visible()->get();
        foreach ($categories_list as $category) {
            $categories[] = [
                'value' => $category->id,
                'label' => $category->title
            ];
        }

        return [
            'categories' => $categories,
        ];
    }
}
