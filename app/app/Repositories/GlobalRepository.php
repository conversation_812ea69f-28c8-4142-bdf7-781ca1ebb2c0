<?php

namespace App\Repositories;

use A17\Twill\Repositories\ModuleRepository;
use function Opis\Closure\unserialize;
use function GuzzleHttp\json_decode;
use Illuminate\Support\Arr;

//use function Aws\serialize;

class GlobalRepository extends ModuleRepository
{

    public function __construct(Branch $model)
    {
        $this->model = $model;
    }


    public function prepareFieldsBeforeSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): array
    {
        return parent::prepareFieldsBeforeSave($object, $fields);
    }

    public function getFormFields(\A17\Twill\Models\Contracts\TwillModelContract $object): array
    {
        $fields = parent::getFormFields($object);
        //dd($fields);
        //SEO Fields
        if ($object->seo_meta) {
            $fields['seo_meta-page_title'] = $object->seo_meta->page_title;
            $fields['seo_meta-page_description'] = $object->seo_meta->page_description;
            $fields['seo_meta-page_keywords'] = $object->seo_meta->page_keywords;
            $fields['seo_meta-change_frequency'] = $object->seo_meta->change_frequency;
            $fields['seo_meta-crawl_priority'] = $object->seo_meta->crawl_priority;
            $fields['seo_meta-show_on_sitemap'] = $object->seo_meta->show_on_sitemap;

            //dd($fields);
        }

        return $fields;
    }

    public function afterSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): void
    {
        //Save SEO
        if ((isset($object->seo_meta_id) || $object->seo_meta_id == NULL) && class_basename($object) != 'Review') {

            $seo_meta = \App\Models\SeoMeta::find($object->seo_meta_id);

            if (!$seo_meta) {
                $seo_meta = new \App\Models\SeoMeta;
            }
            $seo_meta->page_title = $fields['seo_meta-page_title'] ?? '';
            $seo_meta->page_description = $fields['seo_meta-page_description'] ?? '';

            if (isset($fields['seo_meta-page_keywords']) && $fields['seo_meta-page_keywords'] != '') {
                $seo_meta->page_keywords = $fields['seo_meta-page_keywords'];
            } else {
                $seo_meta->page_keywords = '';
            }

            if (isset($fields['seo_meta-crawl_priority'])) {
                $seo_meta->crawl_priority = $fields['seo_meta-crawl_priority'];
            }
            if (isset($fields['seo_meta-change_frequency'])) {
                $seo_meta->change_frequency = $fields['seo_meta-change_frequency'];
            }
            if (isset($fields['seo_meta-show_on_sitemap'])) {
                $seo_meta->show_on_sitemap = $fields['seo_meta-show_on_sitemap'];
            }
            //dd($seo_meta);
            $seo_meta->save();

            $object->seo_meta_id = $seo_meta->id;
            $object->save();
        }
        parent::afterSave($object, $fields);
    }

    public function getSerializedRepeater($fields, $repeaterName, $serializedData)
    {
        $repeater_items = unserialize($fields[$serializedData]);
        $repeatersConfig = config('twill.block_editor.repeaters');
        //dd($repeater_items);
        foreach ($repeater_items as $index => $repeater_item) {
            $repeaters[] = [
                'id' => $repeater_item['id'],
                'type' => $repeatersConfig[$repeaterName]['component'],
                'title' => $repeatersConfig[$repeaterName]['title'],
            ];

            $repeater_fields = Arr::except($repeater_item, ['id', 'medias', 'browsers', 'blocks']);

            foreach ($repeater_fields as $index => $repeater_field) {
                $repeater_data[] = [
                    'name' => 'blocks[' . $repeater_item['id'] . '][' . $index . ']',
                    'value' => $repeater_field
                ];
            }
            //dd($repeater_data);
        }

        $fields['repeaters'][$repeaterName] = $repeaters;
        $fields['repeaterFields'][$repeaterName] = $repeater_data;
        return $fields;
    }

    public function getJsonRepeater($fields, $repeaterName, $serializedData)
    {
        $repeater_items = $fields[$serializedData]; //JSON converted to array using $casts in Model
        $repeatersConfig = config('twill.block_editor.repeaters');
        //dd($repeater_items);
        foreach ($repeater_items as $index => $repeater_item) {
            $id = $repeater_item['id'] ?? $index;

            $repeaters[] = [
                'id' => $id,
                'type' => $repeatersConfig[$repeaterName]['component'],
                'title' => $repeatersConfig[$repeaterName]['title'],
            ];

            $repeater_fields = Arr::except($repeater_item, ['id', 'medias', 'browsers', 'blocks']);

            foreach ($repeater_fields as $index => $repeater_field) {
                $repeater_data[] = [
                    'name' => 'blocks[' . $id . '][' . $index . ']',
                    'value' => $repeater_field
                ];
            }
            //dd($repeater_data);
        }

        $fields['repeaters'][$repeaterName] = $repeaters;
        $fields['repeaterFields'][$repeaterName] = $repeater_data;
        return $fields;
    }
}
