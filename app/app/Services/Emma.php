<?php

namespace App\Services;

use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Client;
use Illuminate\Support\Arr;


class Emma
{

    protected $account_id;
    protected $public_api_key;
    protected $private_api_key;

    function __construct()
    {
        $this->account_id = "1894082";
        $this->public_api_key = "cc8765585de6d21235c3";
        $this->private_api_key = "559ffa14b3c810b39543";
        $this->url = "https://api.e2ma.net/" . $this->account_id . "/members/signup";
    }

    function signup($data)
    {

        foreach ($data['group'] as $index => $group) {
            $data['group_ids'][] = $index;
        }

        $data = Arr::except($data, ['_token', 'Submit', 'group']);
        // dd($data);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_USERPWD, $this->public_api_key . ":" . $this->private_api_key);
        curl_setopt($ch, CURLOPT_URL, $this->url);
        curl_setopt($ch, CURLOPT_POST, count($data));
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSLVERSION, 6);
        $head = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return json_decode($head);
    }
}
