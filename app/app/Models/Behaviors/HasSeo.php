<?php

namespace App\Models\Behaviors;

use App\Models\SeoMeta;

trait HasSeo
{
	//SEO related updates on update and delete
	public static function bootHasSeo()
    {
        static::creating(function($item){
			//Update Sitemap
			self::update_sitemap_event($item);
        });
        static::updating(function($item){
			//Update Sitemap
			self::update_sitemap_event($item);
        });
        static::deleted(function($item){
			//Update Sitemap
			self::delete_seo_event($item);
        });
        static::restored(function($item){
			//Update Sitemap
			self::restore_seo_event($item);
        });
	}
	
	public function seo_meta()
    {
        return $this->belongsTo('App\Models\SeoMeta');
	}

	public static function sitemap_entries() {
		return Self::published()->visible()->whereHas('seo_meta', function ($query) {
			$query->where('show_on_sitemap', true);
		})->orderBy('title', 'ASC')->get();
	}
	
	private static function update_sitemap_event() {
		
	}

	private static function delete_seo_event($item) {
		//Soft delete the SEO
		$seo_meta = SeoMeta::find($item->seo_meta_id);
		if ($seo_meta) {
			$seo_meta->delete();
		}
	}

	private static function restore_seo_event($item) {
		//Soft delete the SEO
		$seo_meta = SeoMeta::onlyTrashed()->where('id', $item->seo_meta_id)->first();
		if ($seo_meta) {
			$seo_meta->restore();
		}
	}
}