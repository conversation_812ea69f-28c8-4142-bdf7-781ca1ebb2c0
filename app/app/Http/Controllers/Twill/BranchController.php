<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;

class BranchController extends ModuleController
{
    protected $moduleName = 'branches';

    protected $previewView = 'frontend.branches.detail';

    /*
     * Options of the index view
     */
    protected $indexOptions = [
        'reorder' => false,
    ];

    /*
     * Relations to eager load for the form view
     * Add relationship used in multiselect and resource form fields
     */
    protected $formWith = [];

    protected $permalinkBase = '';

    /*
     * Filters mapping ('filterName' => 'filterColumn')
     * You can associate items list to filters by having a filterNameList key in the indexData array
     * For example, 'category' => 'category_id' and 'categoryList' => app(CategoryRepository::class)->listAll()
     */
    protected $filters = [
        'province' => 'province'
    ];

    protected $indexColumns = [
        'title' => [ // field column
            'title' => 'Title',
            'field' => 'title',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'slug' => [
            'title' => 'Slug',
            'field' => 'slug',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'province' => [ // field column
            'title' => 'Location',
            'field' => 'province',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
    ];

    protected function indexData($request)
    {
        $provinces = \App\Models\Branch::distinct()->pluck('province');

        if ($provinces->count() > 0) {
            foreach ($provinces as $province) {
                $provinceList[] = [
                    'label' => $province,
                    'value' => $province
                ];
            }
        }

        //dd($provinceList);
        if (isset($provinceList)) {
            return [
                'provinceList' => $provinceList
            ];
        }
    }

    /*
     * Add anything you would like to have available in your module's form view
     * For example, relationship lists for multiselect form fields
     */
    protected function formData($request)
    {
        $staff_members = [];
        $staff_members_list = \App\Models\StaffMember::all();
        foreach ($staff_members_list as $staff_member) {
            $staff_members[] = [
                'value' => $staff_member->id,
                'label' => $staff_member->title
            ];
        }


        $package_categories = [];
        $package_categories_list = \App\Models\PackageCategory::all();
        foreach ($package_categories_list as $package_category) {
            $package_categories[] = [
                'value' => $package_category->id,
                'label' => $package_category->title
            ];
        }
        return [
            'staff_members' => $staff_members,
            'package_categories' => $package_categories
        ];
    }

    protected function previewData($item)
    {
        return [
            'content' => $item
        ];
    }
}
