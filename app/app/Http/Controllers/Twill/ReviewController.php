<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;

class ReviewController extends ModuleController
{
    protected $moduleName = 'reviews';

    /*
     * Can be used in child classes to disable the content editor (full screen block editor)
     */
    protected $disableEditor = true;

    protected $indexColumns = [
        'title' => [ // field column
            'title' => 'Title',
            'field' => 'title',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'branch' => [ // relation column
            'title' => 'Branch Reviewed',
            'sort' => true,
            'relationship' => 'branch',
            'field' => 'title'
        ],
        'treatment' => [ // relation column
            'title' => 'Treatment Reviewed',
            'sort' => true,
            'relationship' => 'treatment',
            'field' => 'title'
        ],
    ];

    /*
     * Add anything you would like to have available in your module's form view
     * For example, relationship lists for multiselect form fields
     */
    protected function formData($request)
    {
        //$itemId = $request->route()->parameters['priceCategory'];
        $branches = [];
        $branch_list = \App\Models\Branch::all();
        foreach ($branch_list as $branch) {
            $branches[] = [
                'value' => $branch->id,
                'label' => $branch->title
            ];
        }

        $treatments = [];
        $treatment_list = \App\Models\Treatment::all();
        foreach ($treatment_list as $treatment) {
            $treatments[] = [
                'value' => $treatment->id,
                'label' => $treatment->title
            ];
        }

        $conditions = [];
        $conditions_list = \App\Models\Condition::all();
        foreach ($conditions_list as $condition) {
            $conditions[] = [
                'value' => $condition->id,
                'label' => $condition->title
            ];
        }

        return [
            'treatments' => $treatments,
            'branches'   => $branches,
            'conditions' => $conditions
        ];
    }
}
