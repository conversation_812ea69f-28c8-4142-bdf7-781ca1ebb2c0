<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

//use App\Models\TreatmentCategory;

class PageController extends Controller
{
    public function index()
    {
        $homepage = Page::where('template', 'home')->first();
        return view('frontend.pages.home', ['page' => $homepage]);
    }

    public function section($slug)
    {
        $content = Page::forSlug($slug)->published()->visible()->first();
        $link_list = [];
        $alternate_view = false;

        if (!$content || ($content->template != 'section-silo') && $content->template != 'section-silo-products' && $content->template != 'section-silo-packages' && $content->template != 'section-silo-branches') {
            return abort(404);
        }

        //Handle the different types of section silos
        switch ($slug) {
            case 'conditions':
                $unformatted_link_list = \App\Models\ConditionCategory::published()->visible()
                    ->orderBy('position', 'asc')
                    ->with('conditions')
                    ->with(['conditions.slugs' => function ($query) {
                        $query->where('active', true);
                    }])
                    ->get()
                    ->toArray();

                foreach ($unformatted_link_list as $link_block) {
                    foreach ($link_block['conditions'] as $index => $condition) {
                        if ($condition['published'] == 0) {
                            unset($link_block['conditions'][$index]);
                        }
                    }
                    $link_list[$link_block['title']] = $link_block['conditions'];
                }


                $template = $content->template;
                break;
                //AB Testig alternate version of conditions silo
            case 'skin-conditions':
                $alternate_view = true;

                $unformatted_link_list = \App\Models\ConditionCategory::published()->visible()
                    ->orderBy('position', 'asc')
                    ->with('conditions')
                    ->with(['conditions.slugs' => function ($query) {
                        $query->where('active', true);
                    }])
                    ->get();

                foreach ($unformatted_link_list as $link_block) {
                    foreach ($link_block['conditions'] as $index => $condition) {
                        if ($condition['published'] == 0) {
                            unset($link_block['conditions'][$index]);
                        }
                    }
                    $link_list[$link_block['title']]['image'] = $link_block->imageAsArray('cover_image');
                    $link_list[$link_block['title']]['items'] = $link_block['conditions']->toArray();
                }
                $template = $content->template;
                break;
            case 'treatments':
                $unformatted_link_list = \App\Models\TreatmentCategory::published()->visible()
                    ->orderBy('position', 'asc')
                    ->with('treatments')
                    ->with(['treatments.slugs' => function ($query) {
                        $query->where('active', true);
                    }])
                    ->get()
                    ->toArray();

                foreach ($unformatted_link_list as $link_block) {
                    foreach ($link_block['treatments'] as $index => $treatment) {
                        if ($treatment['published'] == 0) {
                            unset($link_block['treatments'][$index]);
                        }
                    }
                    $link_list[$link_block['title']] = $link_block['treatments'];
                }
                $template = $content->template;
                break;
                //AB Testig alternate version of treatments silo
            case 'skin-treatments':
                $alternate_view = true;

                $unformatted_link_list = \App\Models\TreatmentCategory::published()->visible()
                    ->orderBy('position', 'asc')
                    ->with('treatments')
                    ->with(['treatments.slugs' => function ($query) {
                        $query->where('active', true);
                    }])
                    ->get();

                foreach ($unformatted_link_list as $link_block) {
                    foreach ($link_block['treatments'] as $index => $treatment) {
                        if ($treatment['published'] == 0) {
                            unset($link_block['treatments'][$index]);
                        }
                    }
                    $link_list[$link_block['title']]['image'] = $link_block->imageAsArray('cover_image');
                    $link_list[$link_block['title']]['items'] = $link_block['treatments']->toArray();
                }
                $template = $content->template;
                break;
            case 'branches':
                $branch_list = \App\Models\Branch::published()->visible()
                    ->orderBy('province', 'asc')
                    ->with(['slugs' => function ($query) {
                        $query->where('active', true);
                    }])
                    ->get();

                $link_list = $branch_list->groupBy('province');

                //Sort branch groupings
                foreach ($link_list as $index => $groups) {
                    $link_list[$index] = $groups->sortBy('title');
                }

                $data['branch_list'] = $branch_list;
                $link_list = $link_list->toArray();
                $template = $content->template;
                break;
            case 'products':;
                $link_list = \App\Models\ProductRange::published()->visible()->orderBy('position', 'asc')->with(['slugs' => function ($query) {
                    $query->where('active', true);
                }])->get();
                $template = $content->template;
                break;
            case 'prices':;
                $data['package_categories'] = \App\Models\PackageCategory::published()->visible()->orderBy('position', 'asc')->get();
                $template = $content->template;
                break;
            case 'about':
                $link_list = config('renewal.about');
                $template = $content->template;
                break;
            default:
                return abort(404);
                break;
        }

        $data['page']      = $content;
        $data['link_list'] = $link_list;
        //Used for A/B testing
        $data['alternate_view'] = $alternate_view;
        $data['slug']      = $slug;

        return view('frontend.pages.' . $template, $data);
    }


    public function orderStaff($staff, $branch)
    {

        $db_staff = DB::table('branch_staff_member')
            ->where(
                [
                    ['branch_id', '=', $branch],
                ]
            )->orderBy('position', 'ASC')
            ->get();

        $return = [];
        foreach ($db_staff as $db_staff_member) {
            foreach ($staff as $staff_member) {

                if ($db_staff_member->staff_member_id == $staff_member->id) {
                    $return[] = $staff_member;
                    break;
                }
            }
        }

        return $return;
    }

    public function orderProducts($products, $branch)
    {

        $db_product = DB::table('branch_product_range')
            ->where(
                [
                    ['branch_id', '=', $branch],
                ]
            )->orderBy('position', 'ASC')
            ->get();

        $return = [];
        foreach ($db_product as $db_product) {
            foreach ($products as $product) {

                if ($db_product->product_range_id == $product->id) {

                    $return[] = $product;
                    break;
                }
            }
        }

        $return = collect($return);

        return $return;
    }

    public function resolve($slug)
    {
        //Handle content that require more that one url param
        $no_direct_access_templates = ['section-silo', 'section-silo-products', 'section-silo-packages', 'home'];
        $no_direct_access_slugs = ['mooimaak-season-1', 'mooimaak-season-two'];

        //Single slug repositories
        $repositories = [
            '\App\Repositories\BranchRepository',
            '\App\Repositories\ConditionRepository',
            '\App\Repositories\PageRepository',
            '\App\Repositories\ProductRangeRepository',
            '\App\Repositories\StaffMemberTypeRepository',
            '\App\Repositories\TreatmentRepository',
        ];

        //Loop throught models found and if they are slugable, test to see if there are any matches for the current url slug
        foreach ($repositories as $repository) {
            $content = app($repository)->forSlug($slug);
            //Exit the loop as soon as content matching slug is found
            if ($content) {
                break;
            }
        }

        //Ensure content exists and that you can directly access content that require more than one URL paramater
        if (!$content || (isset($content->template) && in_array($content->template, $no_direct_access_templates)) || in_array($content->getSlug(), $no_direct_access_slugs)) {
            return abort(404);
        }
        //Handle redirect if slug is inactive
        elseif ($content->redirect) {
            return redirect($content->slug, 301);
        } else {
            switch (class_basename($content)) {
                case 'Branch':
                    $content = $content->load(['staff' => function ($query) {
                        $query->published()->visible();
                    }])->load(['reviews' => function ($query) {
                        $query->published()->orderBy('date_of_visit', 'desc')->limit(10);
                    }])->load(['package_categories' => function ($query) {
                        $query->published()->visible();
                    }]);
                    $content->staff = $this->orderStaff($content->staff, $content->id);
                    $content->product_ranges = $this->orderProducts($content->product_ranges, $content->id);
                    return view('frontend.branches.detail', ['content' => $content]);
                    break;
                case 'Page':
                    return view('frontend.pages.' . $content->template, ['page' => $content]);
                    break;
                case 'ProductRange':
                    $product_ranges = \App\Models\ProductRange::published()->get();
                    return view('frontend.productRanges.detail', [
                        'content' => $content,
                        'product_ranges' => $product_ranges
                    ]);
                    break;
                default:

                    $packages = \App\Models\Package::published()->orderBy('package_category_id', 'ASC')->get();
                    //Automatically find templates for non Page content types
                    return view(
                        'frontend.' . Str::plural(strtolower(class_basename($content))) . '.detail',
                        [
                            'content' => $content,
                            'packages' => $packages

                        ]
                    );
                    break;
            }
        }
    }
}
