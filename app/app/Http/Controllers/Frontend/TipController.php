<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Tip;
use App\Models\StaffMemberType;
use App\Models\Page;

class TipController extends Controller
{
    public function index() {
		$content = Page::forSlug(config('renewal.general.tips_prefix') . '-tips')->published()->visible()->first();
		
		if (!$content) {
			return abort(404);
		}
		
		$tips = Tip::published()->visible()->get();
		$staff_member_types = StaffMemberType::published()->visible()->get();

		return view('frontend.tips.index', [
			'tips' 			     => $tips,
			'staff_member_types' => $staff_member_types,
			'content'            => $content
		]);
	}

	public function detail($slug) {
		$content = Tip::forSlug($slug)->published()->visible()->first();

		if (!$content) {
			return abort(404);
		}

		$tips = Tip::limit(10)->orderBy('created_at', 'ASC')->get();

		return view('frontend.tips.detail', [
			'content' => $content,
			'tips'    => $tips
		]);
	}
}