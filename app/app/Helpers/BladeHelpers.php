<?php

if (! function_exists('ampify')) {
	function ampify($html='') {
		//Convert img to amp-img
		preg_match_all('/<img[^>]+>/i',$html, $imgTags);
		if (!empty($imgTags[0])) {
			for ($i = 0; $i < count($imgTags[0]); $i++) {
				// get the source string
				preg_match('/src="([^"]+)/i',$imgTags[0][$i], $src);
				preg_match('/alt="([^"]+)/i',$imgTags[0][$i], $alt);

				// remove opening 'src=' tag, can`t get the regex right
				$tags['src'] = isset($src[0]) ? str_ireplace( 'src="', '',  $src[0]) : '';
				$tags['alt'] =  isset($alt[0]) ? str_ireplace( 'alt="', '',  $alt[0]) : '';

				$sizes = @getimagesize($tags['src']);
				$amp_img = '<amp-img src="' . $tags['src'] . '" ' . $sizes[3] . ' alt="' . $tags['alt'] . '" layout="fixed"></amp-img>';
				$html = str_replace($imgTags[0][$i], $amp_img, $html);
			}
		}

		//Remove dissallowed html
		$html = str_replace('target=""', '', $html);

		//update links to include amp prefix
		// $html = str_replace('<a href="https://sleep-renewal.platinumseed.com/', '<a href="https://sleep-renewal.platinumseed.com/amp/', $html);
		//

		$pattern = '/(href="https:\/\/sleep-renewal.platinumseed.com)(?!\/amp)./m';
		$replace = 'href="https://sleep-renewal.platinumseed.com/amp/';
		$html = preg_replace($pattern, $replace, $html);
		//Dont change urls for download links
		$html = str_replace('/amp/uploads', '/uploads', $html);

		//Fix canonical link
		$html = str_replace('<link rel="canonical" href="' . url('/') . '/amp', '<link rel="canonical" href="' . url('/'), $html);
		$html = str_replace('<link rel="canonical" href="' . url('/') . '/ />', '<link rel="canonical" href="' . url('/') . '" />', $html);

		return $html;
	}
}

if (! function_exists('cdn')) {
	// global CDN link helper function
	function cdn($asset) {

		$asset = parse_url($asset);
		$asset = $asset['path'];

		// Verify if KeyCDN URLs are present in the config file
		if( !Config::get('renewal.general.cdn') )
			return asset( $asset );

		// Get file name incl extension and CDN URLs
		$cdns = Config::get('renewal.general.cdn');
		$assetName = basename( $asset );

		// Remove query string
		$assetName = explode("?", $assetName);
		$assetName = $assetName[0];

		// Select the CDN URL based on the extension
		foreach( $cdns as $cdn => $types ) {
			if( preg_match('/^.*\.(' . $types . ')$/i', $assetName) )
				return cdnPath($cdn, $asset);
		}

		// In case of no match use the last in the array
		end($cdns);
		return cdnPath( key( $cdns ) , $asset);
	}
}

if (! function_exists('cdnPath')) {
	function cdnPath($cdn, $asset) {
		return  "//" . rtrim($cdn, "/") . "/" . ltrim( $asset, "/");
	}
}
