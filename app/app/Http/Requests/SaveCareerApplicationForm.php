<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SaveCareerApplicationForm extends FormRequest
{


	public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'surname' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'cellphone' => 'required|string|max:20',
            'region' => 'required|string|max:255',
            'position' => 'required|string|max:255',
            'cv' => 'required|extensions:pdf|max:10240', // 10MB max size
			'g-recaptcha-response' => 'recaptcha',
        ];
    }

    public function messages()
    {
        return [
            'cv.required' => 'A CV document is required',
            'cv.mimes' => 'The CV must be a PDF file',
            'cv.max' => 'The CV file may not be larger than 10MB'
        ];
    }
}
