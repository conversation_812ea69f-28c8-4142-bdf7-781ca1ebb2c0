<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleRevisions;
use A17\Twill\Repositories\Behaviors\HandleRepeaters;
use App\Repositories\GlobalRepository;
use App\Models\Mooimaak;

class MooimaakRepository extends GlobalRepository
{
    use HandleSlugs, HandleMedias, HandleRevisions;

    public function __construct(Mooimaak $model)
    {
        $this->model = $model;
    }

    public function prepareFieldsBeforeSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): array
    {
        if (isset($fields['repeaters']['mooimaak_treatments'])) {
			$fields['treatments'] = $fields['repeaters']['mooimaak_treatments'];
		}
        if (isset($fields['repeaters']['mooimaak_team'])) {
			$fields['team'] = $fields['repeaters']['mooimaak_team'];
		}

        return parent::prepareFieldsBeforeSave($object, $fields);
	}
	
    public function getFormFields(\A17\Twill\Models\Contracts\TwillModelContract $object): array
    {
		$fields = parent::getFormFields($object);

		//Repeaters to be serialized
 		if (isset($fields['treatments'])) {
			$fields = $this->getJsonRepeater($fields, 'mooimaak_treatments', 'treatments');
        }
        
		if (isset($fields['team'])) {
			$fields = $this->getJsonRepeater($fields, 'mooimaak_team', 'team');
		}

        return $fields;
    }

	public function afterSave($object, $fields): void {
		//dd($fields);
		// $this->updateBrowser($object, $fields, 'treatments');
		// $this->updateBrowser($object, $fields, 'staff');
		// $this->updateBrowser($object, $fields, 'product_ranges');
		// $this->updateMultiSelect($object, $fields, 'package_categories');
		// parent::afterSave($object, $fields);
	}
}
