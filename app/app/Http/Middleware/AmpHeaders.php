<?php

namespace App\Http\Middleware;

use Closure;

class AmpHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
		$response = $next($request);
		
		$output = $response->getOriginalContent();

		if(is_object($output)) {
			$data = $output->getData();
		
			//Filters specific for amp output
			if($data['hasAmpUrl']) {
				$response->header("Access-Control-Allow-Origin", '*'); 
				$response->header("Access-Control-Allow-Methods", "POST, GET, OPTIONS");
				$response->header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token");
				$response->header("Access-Control-Expose-Headers", "AMP-Access-Control-Allow-Source-Origin");
				$response->header("AMP-Access-Control-Allow-Source-Origin", 'https://sleeprenewal.lndo.site:444');
				$response->header("Access-Control-Allow-Credentials", "true");
			}
		}

        return $response;
    }
}
