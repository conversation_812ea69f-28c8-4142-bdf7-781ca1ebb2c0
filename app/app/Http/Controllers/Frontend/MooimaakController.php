<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Mooimaak;
use App\Models\Page;

class MooimaakController extends Controller
{
    public function index($season) {
        $listing = Mooimaak::where('season', $season)->published()->visible()->get();
        if ($listing->count() == 0) {
            return abort(404);
        }

        $content = Page::forSlug('mooimaak-season-' . $season)->published()->visible()->first();
        
        return view('frontend.mooimaaks.index', [
            'content' => $content,
            'listing' => $listing,
            'season' => $season,
			'seasons' => [1, 2] 
        ]);
    }

    public function detail($contestant) {
        $content = Mooimaak::forSlug($contestant)->published()->visible()->first();

		if (!$content) {
			return abort(404);
		}

        // get previous mooimaak
        // $previous = Mooimaak::where('id', '<', $content->id)
        //     ->where('season', $content->season)
        //     ->published()
        //     ->visible()
        //     ->orderBy('id','asc')
        //     ->first();

        // // get next mooimaak
        // $next = Mooimaak::where('id', '>', $content->id)
        //     ->where('season', $content->season)
        //     ->published()
        //     ->visible()
        //     ->orderBy('id','asc')
        //     ->first();

		$listing = Mooimaak::where('season', $content->season)->published()->orderBy('publish_start_date', 'asc')->get();

        return view('frontend.mooimaaks.detail', [
            'content'   => $content,
            'listing' => $listing,
			'seasons' => [1, 2] 
        ]);
    }
}
