<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleBlocks;
use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleFiles;
use A17\Twill\Repositories\Behaviors\HandleRevisions;
//use A17\Twill\Repositories\Behaviors\Sortable;
use App\Repositories\GlobalRepository;
use App\Models\Treatment;
use function GuzzleHttp\json_encode;

class TreatmentRepository extends GlobalRepository
{
    use HandleBlocks, HandleSlugs, HandleMedias, HandleFiles, HandleRevisions;

    public function __construct(Treatment $model)
    {
        $this->model = $model;
	}

	// implement the getFormFields method
	public function getFormFields(\A17\Twill\Models\Contracts\TwillModelContract $object): array {
		// don't forget to call the parent getFormFields function
		$fields = parent::getFormFields($object);

		//related conditions
		$fields['browsers']['conditions'] = $this->getFormFieldsForBrowser($object, 'conditions', 'content');

		//Q and A repeater
		if (isset($fields['treatments_questions_and_answers']) && !empty($fields['treatments_questions_and_answers'])) {
			$fields = $this->getJsonRepeater($fields, 'question_and_answer', 'treatments_questions_and_answers');
		}

		//Selectcast Repeater
		if (isset($fields['selectcast_embeds']) && !empty($fields['selectcast_embeds'])) {
			$fields = $this->getJsonRepeater($fields, 'selectcast_embed', 'selectcast_embeds');
		}

		if (isset($fields['related_packages']) && !empty($fields['related_packages'])) {
			$fields = $this->getJsonRepeater($fields, 'treatment_related_packages', 'related_packages');
		}

		// return fields
		return $fields;
	}
	
	public function prepareFieldsBeforeSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): array
    {

        if (isset($fields['repeaters']['question_and_answer'])) {
			$fields['treatments_questions_and_answers'] = $fields['repeaters']['question_and_answer'];
		}

		if (isset($fields['repeaters']['treatment_related_packages'])) {
			$fields['related_packages'] = $fields['repeaters']['treatment_related_packages'];
		}

		if (isset($fields['repeaters']['selectcast_embed'])) {
			$fields['selectcast_embeds'] = $fields['repeaters']['selectcast_embed'];
		}

        return parent::prepareFieldsBeforeSave($object, $fields);
	}

	// implement the filter method
	public function filter(\Illuminate\Database\Eloquent\Builder $query, array $scopes = []): \Illuminate\Database\Eloquent\Builder {

		if (isset($scopes['category'])) {
			$query->whereHas('category', function ($query) use ($scopes) {
				$query->where('id', $scopes['category']);
			});
			unset($scopes['category']);
		}

		return parent::filter($query, $scopes);
	}

	public function afterSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): void {
		$this->updateMultiSelect($object, $fields, 'package_categories');
		parent::afterSave($object, $fields);
	}
}
