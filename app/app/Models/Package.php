<?php

namespace App\Models;

use A17\Twill\Models\Behaviors\HasBlocks;
use A17\Twill\Models\Behaviors\HasSlug;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasFiles;
use A17\Twill\Models\Behaviors\HasRevisions;
use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Behaviors\Sortable;
use App\Models\Behaviors\HasSeo;
use A17\Twill\Models\Model;
use Laravel\Scout\Searchable;

class Package extends Model implements Sortable
{
    use HasBlocks, HasSlug, HasMedias, HasFiles, HasRevisions, HasPosition, HasSeo;

    protected $fillable = [
        'published',
        'title',
        'description',
        'position',
        'publish_start_date',
        'publish_end_date',
        'price',
        'special_price',
        'colour',
        'external_related_treatments',
        'treatment_recovery_time',
        'treatment_specialist',
        'procedure_time',
        'best_results',
        'text_colour',
        'package_category_id',
        'skin_types',
        'featured',
    ];

    public $mediasParams = [
        'image' => [
            'default' => [
                [
                    'name' => 'desktop',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ],
            'mobile' => [
                [
                    'name' => 'mobile',
                    'ratio' => 1,
                ],
            ],
        ],
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = [];

    // uncomment and modify this as needed if you use the HasSlug trait
    public $slugAttributes = [
        'title',
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
    ];

    //Return JSON data as an array
    protected $casts = [
        'external_related_treatments' => 'array',
    ];

    public function category()
    {
        return $this->belongsTo(\App\Models\PackageCategory::class, 'package_category_id');
    }

    public function shouldBeSearchable()
    {
        return $this->published;
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $fullArray = $this->toArray();

        $fullArray = $this->transform($fullArray);

        // Customize array...
        return [
            'title'       => $fullArray['title'],
            'description' => $fullArray['description'] ?? '',
            'category'    => $this->category->title ?? ''
        ];
    }
}
