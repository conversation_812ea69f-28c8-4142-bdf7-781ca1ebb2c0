<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\StaffMember;
use App\Models\Treatment;
use App\Models\ProductRange;
use App\Models\Condition;
use App\Models\Review;
use App\Models\Tip;
use App\Models\Page;
use App\Models\TreatmentCategory;
use App\Models\Product;
use App\Models\StaffMemberType;
use App\Models\PackageCategory;
use App\Models\Package;
use App\Models\Mooimaak;

use Illuminate\Support\Facades\Storage;

use Carbon\Carbon;
use App\Models\ConditionCategory;
use Illuminate\Support\Str;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleFiles;

class ImportController extends Controller
{
	protected $model;
	protected $site_prefix;

	use HandleMedias, HandleFiles;
	//Order:
	// 1 - treatments
	// 2 - conditions
	// 3 - staff
	// 4 - branches
	// 5 - reviews
	// 6 - sleep tips
	// 7 - pages

	public function __construct()
	{
		$this->site_prefix = Str::slug(config('app.name'));
	}

	public function branches()
	{
		$this->model = new Branch;

		$path = public_path('imports/' . $this->site_prefix . '/branches.json');
		$branches = json_decode(file_get_contents($path), true);

		foreach($branches['branches'] as $branch) {
			if (isset($branch['categories'][1])) {
				$branch['province'] = $branch['categories'][1];
				$branch = $this->twill_defaults($branch);

				//Set the branch manager
				$branch_manager = StaffMember::firstOrCreate(['title' => $branch['branch_manager']['title']]);
				$branch['branch_manager_id'] = $branch_manager->id;

				$new_branch = Branch::updateOrCreate(['title' => $branch['title']], $branch);
				$this->saveSeo($branch['seo'], $new_branch);

				$this->saveSlug($new_branch, $branch);

				//Import images
				$filefields = [];
				if ($branch['branch_gallery'] != null) {
					foreach ($branch['branch_gallery'] as $gallery) {
						$upload = $this->importImage($gallery['image']);
						$filefields['medias']['slideshow'][] = [
							'id' => $upload->id
						];
					}
				}

				if (!empty($filefields)) {
					//dd($filefields);
					$this->afterSaveHandleMedias($new_branch, $filefields);
				}

				//Save relationships
				$this->updateOrderedBelongsTomany($branch['branch_staff'], StaffMember::class, $new_branch, 'staff');
				$this->updateOrderedBelongsTomany($branch['treatments_available'], Treatment::class, $new_branch, 'treatments');
				$this->updateOrderedBelongsTomany($branch['product_ranges'], ProductRange::class, $new_branch, 'product_ranges');
			}
		}

		return "done";
	}

	public function staff()
	{
		$this->model = new StaffMember;

		$path = public_path('imports/' . $this->site_prefix . '/staff.json');
		$staff_members = json_decode(file_get_contents($path), true);
		//dd($staff_members);
		foreach($staff_members['staff'] as $staff_member) {
			$staff_member = $this->twill_defaults($staff_member);

			$staff_member['description'] = $this->formatContent($staff_member['description']);

			//save type
			$type = StaffMemberType::firstOrCreate(['title' => ucwords(str_replace('-', ' ', $staff_member['type']))]);
			$staff_member['staff_members_type_id'] = $type->id;

			$new_staff = StaffMember::updateOrCreate(['title' => $staff_member['title']], $staff_member);
			$this->saveSeo($staff_member['seo'], $new_staff);
			$this->saveSlug($new_staff, $staff_member);

			//Import images
			$filefields = [];
			if ($staff_member['main_image'] != null) {
				$upload = $this->importImage($staff_member['main_image']);
				$filefields['medias']['cover'][] = [
					'id' => $upload->id
				];
			}

			if (!empty($filefields)) {
				//dd($filefields);
				$this->afterSaveHandleMedias($new_staff, $filefields);
			}
		}

		return "done";
	}

	public function products()
	{
		$this->model = new Product;

		$path = public_path('imports/' . $this->site_prefix . '/products.json');
		$products = json_decode(file_get_contents($path), true);
		//dd($products);
		foreach($products['products'] as $product) {
			$product = $this->twill_defaults($product);

			$product['title'] = stripslashes($product['title']);
			$product['description'] = $this->formatContent($product['content']);
			$product['buy_now_url'] = $product['store_url'] ?? null;

			if (isset($product['categories'][1])) {
				$product_range = ProductRange::where(['title' => $product['categories'][1]])->first();
				$product['product_range_id'] = $product_range->id;

				if($product_range->external_url != null) {
					$product['published'] == 0;
				}

				$new_product = Product::updateOrCreate(['title' => $product['title']], $product);
				$this->saveSeo($product['seo'], $new_product);

				//Import images
				$filefields = [];
				if ($product['product_image'] != null) {
					$upload = $this->importImage($product['product_image']);
					$filefields['medias']['cover'][] = [
						'id' => $upload->id
					];
				}

				if (!empty($filefields)) {
					$this->afterSaveHandleMedias($new_product, $filefields);
				}
			}
		}

		return "done";
	}

	public function product_ranges()
	{
		$this->model = new ProductRange();

		$path = public_path('imports/' . $this->site_prefix . '/product-ranges.json');
		$product_ranges = json_decode(file_get_contents($path), true);
		//dd($products);
		foreach($product_ranges['product_ranges'] as $product_range) {
			$product_range = $this->twill_defaults($product_range);

			$product_range['description'] = $this->formatContent($product_range['description']);

			$new_product_range = ProductRange::updateOrCreate(['title' => $product_range['title']], $product_range);

			//Import images
			$filefields = [];
			if ($product_range['product_range_image'] != null) {
				$upload = $this->importImage($product_range['product_range_image']);
				$filefields['medias']['cover'][] = [
					'id' => $upload->id
				];
			}

			if (!empty($filefields)) {
				//dd($filefields);
				$this->afterSaveHandleMedias($new_product_range, $filefields);
			}
		}

		return "done";
	}

	public function conditions()
	{
		$this->model = new Condition();
		$path = public_path('imports/' . $this->site_prefix . '/conditions.json');
		$conditions = json_decode(file_get_contents($path), true);

		foreach($conditions['conditions'] as $condition) {
			//Only import if a category has been assigned
			if (isset($condition['categories'][1])) {
				$condition = $this->twill_defaults($condition);

				//Set the treatment category
				$condition_category = ConditionCategory::firstOrCreate(['title' => $condition['categories'][1]]);
				$condition['condition_category_id'] = $condition_category->id;

				$condition['conditions_content'] = $this->formatContent($condition['conditions_content']);

				//dd($condition);
				foreach($condition['conditions_questions_and_answers'] as $index => $question_answer) {
					$condition['conditions_questions_and_answers'][$index]['question'] = $this->formatContent($question_answer['question']);
					$condition['conditions_questions_and_answers'][$index]['answer'] = $this->formatContent($question_answer['answer']);
				}

				$new_conditions = Condition::updateOrCreate(['title' => $condition['title']], $condition);
				$this->saveSeo($condition['seo'], $new_conditions);
				$this->saveSlug($new_conditions, $condition);

				//Save relationships
				$this->updateOrderedBelongsTomany($condition['related_treatments'], Treatment::class, $new_conditions, 'treatments');
			}


			//Import images
			$imageFiles = [];
			if ($condition['images'] != null) {
				foreach ($condition['images'] as $gallery) {
					$upload = $this->importImage($gallery['url']);
					$imageFiles['medias']['slideshow'][] = [
						'id' => $upload->id
					];
				}
			}

			if (!empty($imageFiles)) {
				$this->afterSaveHandleMedias($new_conditions, $imageFiles);
			}

		}

		return "done";
	}

	public function treatments()
	{
		$this->model = new Treatment();
		$path = public_path('imports/' . $this->site_prefix . '/treatments.json');
		$treatments = json_decode(file_get_contents($path), true);

		foreach($treatments['treatments'] as $index => $treatment) {

			//Only handle treatments with proper categories assigned
			if (isset($treatment['categories'][1])) {
				$treatment = $this->twill_defaults($treatment);

				//Set the treatment category
				$treatment_category = TreatmentCategory::firstOrCreate(['title' => $treatment['categories'][1]], ['published' => 1]);
				$treatment['treatment_category_id'] = $treatment_category->id;

				$treatment['treatments_content'] = $this->formatContent($treatment['treatments_content']);

				foreach($treatment['treatments_questions_and_answers'] as $index => $question_answer) {
					$treatment['treatments_questions_and_answers'][$index]['question'] = $this->formatContent($question_answer['question']);
					$treatment['treatments_questions_and_answers'][$index]['answer'] = $this->formatContent($question_answer['answer']);
				}

				if (isset($treatment['videos'][1])) {
					$treatment['treatments_video'] = $treatment['videos'][1]['embed'];
					//dd($treatment);
				}

				$new_treatments = Treatment::updateOrCreate(['title' => $treatment['title']], $treatment);
				$this->saveSeo($treatment['seo'], $new_treatments);
				$this->saveSlug($new_treatments, $treatment);

				//Import images
				$imageFiles = [];
				if ($treatment['images'] != null) {
					foreach ($treatment['images'] as $gallery) {
						$upload = $this->importImage($gallery['url']);
						$imageFiles['medias']['slideshow'][] = [
							'id' => $upload->id
						];
					}
				}

				if ($treatment['treatment_picture'] != null) {
					$upload = $this->importImage($treatment['treatment_picture']);
					$imageFiles['medias']['treatment_image'][] = [
						'id' => $upload->id
					];
				}

				if (!empty($imageFiles)) {
					//dd($imageFiles);
					$this->afterSaveHandleMedias($new_treatments, $imageFiles);
				}

				$files = [];
				if($treatment['brochure'] != null) {
					$upload = $this->importFile($treatment['brochure']);
					$files['medias']['brochure'][] = [
						'id' => $upload->id
					];
				}

				if (!empty($files)) {
					$this->afterSaveHandleFiles($new_treatments, $files);
				}
			}

		}

		return "done";
	}

	public function reviews()
	{
		$path = public_path('imports/' . $this->site_prefix . '/reviews.json');
		$reviews = json_decode(file_get_contents($path), true);
		//dd($reviews);
		foreach($reviews['reviews'] as $review) {
			$review = $this->twill_defaults($review);

			$review['description'] = $this->formatContent($review['description']);

			//convert "yes" and "no" values to boolean
			$review['display_on_treatment_page'] = filter_var($review['display_on_treatment_page'], FILTER_VALIDATE_BOOLEAN);
			$review['display_on_branch_page'] = filter_var($review['display_on_branch_page'], FILTER_VALIDATE_BOOLEAN);
			$review['display_on_conditions_page'] = filter_var($review['display_on_conditions_page'], FILTER_VALIDATE_BOOLEAN);
			$review['display_author_name'] = filter_var($review['display_author_name'], FILTER_VALIDATE_BOOLEAN);
			$review['consent_to_publish'] = filter_var($review['consent_to_publish'], FILTER_VALIDATE_BOOLEAN);

			//Set the reviewed branch
			$branch_reviewed = Branch::where(['title' => $review['branch_reviewed']['title']])->first();

			if ($branch_reviewed) {
				$review['branch_id'] = $branch_reviewed->id;
			}

			//Set the reviewed treatment
			$treatment_reviewed = Treatment::where(['title' => $review['treatment_reviewed']['title']])->first();

			if ($treatment_reviewed) {
				$review['treatment_id'] = $treatment_reviewed->id;
			}

			//Set the reviewed condition
			$condition_reviewed = Condition::where(['title' => $review['condition_reviewed']['title']])->first();

			if ($condition_reviewed) {
				$review['condition_id'] = $condition_reviewed->id;
			}

			$new_review = Review::updateOrCreate(['title' => $review['title']], $review);
		}

		return "done";
	}

	public function tips()
	{
		$path = public_path('imports/' . $this->site_prefix . '/tips.json');
		$tips = json_decode(file_get_contents($path), true);
		foreach($tips['tips'] as $tip) {
			$tip = $this->twill_defaults($tip);

			$tip['summary'] = $this->formatContent($tip['summary']);
			$tip['description'] = $this->formatContent($tip['description']);

			$new_tips = Tip::updateOrCreate(['title' => $tip['title']], $tip);
			$this->saveSeo($tip['seo'], $new_tips);
			$this->saveSlug($new_tips, $tip);
		}

		return "done";
	}

	public function pages()
	{
		$path = public_path('imports/' . $this->site_prefix . '/pages.json');
		$pages = json_decode(file_get_contents($path), true);

		foreach($pages['pages'] as $page) {
			$page = $this->twill_defaults($page);
			$page['template'] = 'default';
			//remove slashes added during export
			$page['content'] = $this->formatContent($page['content']);

			$new_page = Page::updateOrCreate(['title' => $page['title']], $page);
			$this->saveSeo($page['seo'], $new_page);
			$this->saveSlug($new_page, $page);
		}

		return "done";
	}

	public function packages_categories()
	{
		$this->model = new PackageCategory();
		$path = public_path('imports/' . $this->site_prefix . '/packages-categories.json');
		$package_categories = json_decode(file_get_contents($path), true);

		foreach($package_categories['package_categories'] as $package_category) {
			$package_category = $this->twill_defaults($package_category);
			$package_category['description'] = $this->formatContent($package_category['description']);
			//dd($package_category);
			$new_package_category = PackageCategory::updateOrCreate(['title' => $package_category['title']], $package_category);

			$this->saveSeo($package_category['seo'], $new_package_category);

			$this->saveSlug($new_package_category, $package_category);

			//Import images
			$filefields = [];
			if ($package_category['image'] != null) {
				$upload = $this->importImage($package_category['image']);
				$filefields['medias']['cover'][] = [
					'id' => $upload->id
				];
			}
			if (!empty($filefields)) {
				$this->afterSaveHandleMedias($new_package_category, $filefields);
			}
		}

		return "done";
	}

	public function packages()
	{
		$this->model = new Package();
		$path = public_path('imports/' . $this->site_prefix . '/packages.json');
		$packages = json_decode(file_get_contents($path), true);

		foreach($packages['packages'] as $package) {
			$package = $this->twill_defaults($package);
			$package['description'] = $this->formatContent($package['description']);
			$package['price'] = (int)str_replace(' ', '', $package['price']);
			$package['special_price'] = (int)str_replace(' ', '', $package['special_price']);

			//Set the package category
			$package_category = PackageCategory::firstOrCreate(['title' => $package['category']]);
			$package['package_category_id'] = $package_category->id;

			$new_package = Package::updateOrCreate(['title' => $package['title']], $package);

			$this->saveSeo($package['seo'], $new_package);

			$this->saveSlug($new_package, $package);
		}

		return "done";
	}

	public function mooimaak()
	{
		$this->model = new Mooimaak();
		$path = public_path('imports/' . $this->site_prefix . '/mooimaak.json');
		$mooinaaks = json_decode(file_get_contents($path), true);

		foreach($mooinaaks['mooimaak'] as $mooimaak) {
			$mooimaak = $this->twill_defaults($mooimaak);
			$new_mooimaak = Mooimaak::updateOrCreate(['title' => $mooimaak['title']], $mooimaak);

			$this->saveSeo($mooimaak['seo'], $new_mooimaak);
			$this->saveSlug($new_mooimaak, $mooimaak);

			//Import images
			$filefields = [];
			if ($mooimaak['listing_before_pic'] != null) {
				$upload = $this->importImage($mooimaak['listing_before_pic']);
				$filefields['medias']['listing_before_pic'][] = [
					'id' => $upload->id
				];
			}
			if ($mooimaak['listing_after_pic'] != null) {
				$upload = $this->importImage($mooimaak['listing_after_pic']);
				$filefields['medias']['listing_after_pic'][] = [
					'id' => $upload->id
				];
			}
			if ($mooimaak['banner_pic_1'] != null) {
				$upload = $this->importImage($mooimaak['banner_pic_1']);
				$filefields['medias']['banner_pic_1'][] = [
					'id' => $upload->id
				];
			}
			if ($mooimaak['banner_pic_2'] != null) {
				$upload = $this->importImage($mooimaak['banner_pic_2']);
				$filefields['medias']['banner_pic_2'][] = [
					'id' => $upload->id
				];
			}
			if ($mooimaak['detail_before_pic'] != null) {
				$upload = $this->importImage($mooimaak['detail_before_pic']);
				$filefields['medias']['detail_before_pic'][] = [
					'id' => $upload->id
				];
			}
			if ($mooimaak['detail_after_pic'] != null) {
				$upload = $this->importImage($mooimaak['detail_after_pic']);
				$filefields['medias']['detail_after_pic'][] = [
					'id' => $upload->id
				];
			}
			if ($mooimaak['eyes_pic'] != null) {
				$upload = $this->importImage($mooimaak['eyes_pic']);
				$filefields['medias']['eyes_pic'][] = [
					'id' => $upload->id
				];
			}

			if (!empty($filefields)) {
				$this->afterSaveHandleMedias($new_mooimaak, $filefields);
			}

		}

		return "done";
	}

	private function saveSeo($data, $object)
	{
		$seo_meta = \App\Models\SeoMeta::where('id', $object->seo_meta_id)->orWhere('page_title', $data['title'])->first();

		if(!$seo_meta) {
			$seo_meta = new \App\Models\SeoMeta;
		}

		$seo_meta->page_title = $data['title'];
		$seo_meta->page_description = $data['description'];
		$seo_meta->save();

		$object->seo_meta_id = $seo_meta->id;
		$object->save();
	}

	private function twill_defaults($data)
	{
		//$data['slug']['en'] = $data['url_title'];
		$data['publish_start_date'] = Carbon::now();
		$data['published'] = 1;

		return $data;
	}

	private function updateOrderedBelongsTomany($data, string $related_object, $object, string $relationship) {

		//$position = $object->$relationship()->newPivotStatement()->max('position');
		$position = 1;
		$relatedElementsWithPosition = [];
        foreach ($data as $relatedElement) {
			$relation = $related_object::firstOrNew(['title' => $relatedElement['title']]);
			$relation->published = 1;
			$relation->save();
            $relatedElementsWithPosition[$relation->id] = ['position' => $position++];
		}
		$object->$relationship()->sync($relatedElementsWithPosition);
	}

	private function saveSlug($object, $fields)
	{
		//Set slug
		$object->updateOrNewSlug([
			"slug" => $fields['url_title'],
			"locale" => "en",
			"active" => 1
		]);
	}

	private function importImage($url)
	{
		$model = new \A17\Twill\Models\Media;
		
		$url = str_replace('http://sleeprenewal.local', 'https://sleeprenewal.co.za', $url);
		$url = str_replace('https://skinrenewal.local', 'https://skinrenewal.co.za', $url);
		$url = str_replace('http://healthrenewal.local', 'https://healthrenewal.co.za', $url);
		$url = str_replace('http://bodyrenewal.local', 'https://bodyrenewal.co.za', $url);
		$url = str_replace('http://brainrenewal.local', 'https://brainrenewal.co.za', $url);
		$url = str_replace('hairrenewal.co.za', 'hairrenewal-old.lndo.site', $url);
		$contents = file_get_contents($url);

		$info = pathinfo($url);
		$filename = sanitizeFilename($info['basename']);

		//Check if the file already exists
		$existing = $model->where('filename', $info['basename'])->first();
		if (!$existing) {
			$unique_folder_name = Str::uuid();
			$fileDirectory = config('twill.media_library.local_path') . $unique_folder_name;
			$local_file = $fileDirectory . '/' . $filename;

			$stored = Storage::disk('libraries')->put($local_file, $contents);

			if ($stored) {
				list($w, $h) = getimagesize($fileDirectory . '/' . $filename);

				$fields = [
					'uuid' => config('twill.media_library.local_path') . $unique_folder_name . '/' . $filename,
					'filename' => $info['basename'],
					'width' => $w,
					'height' => $h,
				];


				$repository = new \A17\Twill\Repositories\MediaRepository($model);
				$fields = $repository->prepareFieldsBeforeCreate($fields);

				return $model->create($fields);
			}
		}
		return $existing;
	}

	public function importFile($url)
    {
		$model = new \A17\Twill\Models\File;

		$url = str_replace('http://sleeprenewal.local', 'https://sleeprenewal.co.za', $url);
		$url = str_replace('https://skinrenewal.local', 'https://skinrenewal.co.za', $url);
		$url = str_replace('http://healthrenewal.local', 'https://healthrenewal.co.za', $url);
		$url = str_replace('http://bodyrenewal.local', 'https://bodyrenewal.co.za', $url);
		$url = str_replace('http://brainrenewal.local', 'https://brainrenewal.co.za', $url);
		$contents = file_get_contents($url);

		$info = pathinfo($url);
		$filename = sanitizeFilename($info['basename']);
        $cleanFilename = preg_replace("/\s+/i", "-", $filename);

		//Check if the file already exists
		$existing = $model->where('filename', $info['basename'])->first();
		if (!$existing) {
			$unique_folder_name = Str::uuid();
			$fileDirectory = config('twill.file_library.local_path') . $unique_folder_name;
			$local_file = $fileDirectory . '/' . $filename;

			$stored = Storage::disk('libraries')->put($local_file, $contents);

			if ($stored) {
				$fields = [
					'uuid' => $local_file,
					'filename' => $cleanFilename,
					'size' => Storage::disk('libraries')->size($local_file),
				];
			}
			return $model->create($fields);
		}
		return $existing;
    }

	//Remove formatting added during export and replace url's embedded within content
	private function formatContent($content) {
		$content = stripslashes($content);
		$content = str_replace('http://healthrenewal.local', url()->to('/'), $content);
		//Remove some invalid content added in EE
		$content = str_replace('initial;"=""', '', $content);
		$content = str_replace('initial;"', '', $content);
		$content = str_replace('center;"=""', '', $content);
		$content = str_replace('center;"', '', $content);
		$content = str_replace('<h>', '', $content);
		$content = str_replace('</h>', '', $content);
		$content = str_replace('style="background-color: ', '', $content);
		$content = str_replace('<g>', '', $content);
		$content = str_replace('</g>', '', $content);
		return $content;
	}

	public function shouldIgnoreFieldBeforeSave($ignore)
    {
        return false;
	}

	public function getCrops($role)
    {
        return $this->model->mediasParams[$role];
    }
}
