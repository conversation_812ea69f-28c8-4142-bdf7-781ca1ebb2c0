<?php

return [
    'enabled' => [
        'users-management' => true,
        'media-library' => true,
        'file-library' => true,
        'dashboard' => true,
        'search' => true,
        'block-editor' => true,
        'buckets' => true,
        'users-image' => false,
        'users-description' => false,
        'site-link' => false,
        'settings' => true,
        'activitylog' => true,
    ],

    'block_editor' => [
        'files' => ['recipes'],
        'block_single_layout' => 'frontend.layouts.block',
        'block_views_path' => 'frontend.blocks',
        'block_views_mappings' => [],
        'repeaters' => [
            'addressline' => [
                'title' => 'Address',
                'trigger' => 'Add address line',
                'component' => 'a17-block-addressline',
                'max' => 4,
            ],
            'question_and_answer' => [
                'title' => 'Question / Answer',
                'trigger' => 'Add a question and answer',
                'component' => 'a17-block-question_and_answer',
                'max' => 100,
            ],
            'product' => [
                'title' => 'Product',
                'trigger' => 'Add a product',
                'component' => 'a17-block-product',
                'max' => 100,
            ],
            'external_related_treatment' => [
                'title' => 'Related Treatment',
                'trigger' => 'Add a related treatment',
                'component' => 'a17-block-external_related_treatment',
                'max' => 100,
            ],
            'mooimaak_treatments' => [
                'title' => 'Treatments',
                'trigger' => 'Add Treatment',
                'component' => 'a17-block-mooimaak_treatments',
                'max' => 15,
            ],
            'mooimaak_team' => [
                'title' => 'Team',
                'trigger' => 'Add Team Member',
                'component' => 'a17-block-mooimaak_team',
                'max' => 20,
            ],
            'treatment_related_packages' => [
                'title' => 'Related Packages',
                'trigger' => 'Add A Package',
                'component' => 'a17-block-treatment_related_packages',
                'max' => 20,
            ],
            'branch_packages' => [
                'title' => 'Branch Packages',
                'trigger' => 'Add Package',
                'component' => 'a17-block-branch_packages',
                'max' => 50,
            ],
            'selectcast_embed' => [
                'title' => 'Video Embed',
                'trigger' => 'Add Video',
                'component' => 'a17-block-selectcast_embed',
                'max' => 10,
            ],
        ],
        'blocks' => [
            'addresslines' => [
                'title' => 'Address',
                'icon' => 'text',
                'component' => 'a17-block-addresslines',
            ],
            'questions_and_answers' => [
                'title' => 'Questions and Answers',
                'icon' => 'text',
                'component' => 'a17-block-questions_and_answers',
            ],
            'link' => [
                'title' => 'Link',
                'icon' => 'text',
                'group' => 'pages',
                'component' => 'a17-block-link',
            ],
            'products' => [
                'title' => 'Products',
                'icon' => 'text',
                'component' => 'a17-block-products',
            ],
            'form' => [
                'title' => 'Form Embed',
                'icon' => 'text',
                'group' => 'pages',
                'component' => 'a17-block-form',
            ],
            'content' => [
                'title' => 'Content Block',
                'icon' => 'content-editor',
                'group' => 'pages',
                'component' => 'a17-block-content',
            ],
            'image' => [
                'title' => 'Image Block',
                'icon' => 'image',
                'group' => 'pages',
                'component' => 'a17-block-image',
            ],
            'section_title' => [
                'title' => 'Section Title',
                'icon' => 'text',
                'component' => 'a17-block-section_title',
            ],
            'selectcast_video' => [
                'title' => 'Video',
                'icon' => 'text',
                'component' => 'a17-block-selectcast_video',
            ],
            'youtube_video' => [
                'title' => 'Youtube Video',
                'icon' => 'text',
                'component' => 'a17-block-youtube_video',
            ],
            'table' => [
                'title' => 'Table',
                'icon' => 'content-editor',
                'component' => 'a17-block-table',
            ],
        ],
        'browser_route_prefixes' => [
            'treatments' => 'content',
            'influencers' => 'content',
        ],
    ],

    'dashboard' => [
        'modules' => [
            'App\Models\Branch' => [
                'name' => 'branches',
                'label' => 'Branches',
                'label_singular' => 'Branch',
                'routePrefix' => 'content',
                'count' => true,
                'create' => true,
                'activity' => true,
                'search' => true,
                'search_fields' => ['title', 'branch_intro', 'branch_email']
            ],
            'App\Models\Condition' => [
                'name' => 'conditions',
                'label' => 'Conditions',
                'label_singular' => 'Condition',
                'routePrefix' => 'content',
                'count' => true,
                'create' => true,
                'activity' => true,
                'search' => true,
                'search_fields' => ['title', 'conditions_featured_text', 'conditions_content', 'conditions_questions_and_answers']
            ],
            'App\Models\Package' => [
                'name' => 'packages',
                'label' => 'Packages',
                'label_singular' => 'Package',
                'routePrefix' => 'content',
                'create' => true,
                'activity' => true,
                'search' => true,
                'search_fields' => ['title', 'description']
            ],
            'App\Models\PackageCategory' => [
                'name' => 'packageCategories',
                'label' => 'Package Categories',
                'label_singular' => 'Package Category',
                'routePrefix' => 'categories',
                'create' => true,
                'activity' => true,
                'search' => true,
                'search_fields' => ['title', 'description']
            ],
            'App\Models\Product' => [
                'name' => 'products',
                'label' => 'Products',
                'label_singular' => 'Product',
                'routePrefix' => 'content',
                'create' => true,
                'activity' => true,
                'search' => true,
                'search_fields' => ['title', 'description']
            ],
            'App\Models\ProductRange' => [
                'name' => 'productRanges',
                'label' => 'Product Ranges',
                'label_singular' => 'Product Range',
                'routePrefix' => 'categories',
                'create' => true,
                'activity' => true,
                'search' => true,
                'search_fields' => ['title', 'description', 'external_url']
            ],
            'App\Models\Treatment' => [
                'name' => 'treatments',
                'label' => 'Treatments',
                'label_singular' => 'Treatment',
                'routePrefix' => 'content',
                'count' => true,
                'create' => true,
                'activity' => true,
                'search' => true,
                'search_fields' => ['title', 'treatments_content', 'treatments_featured_text', 'treatments_questions_and_answers']
            ],
            'App\Models\StaffMember' => [
                'name' => 'staffMembers',
                'label' => 'Staff Members',
                'label_singular' => 'Staff Member',
                'routePrefix' => 'content',
                'count' => true,
                'create' => true,
                'activity' => true,
                'search' => true,
                'search_fields' => ['title', 'description']
            ],
            'App\Models\Page' => [
                'name' => 'pages',
                'label' => 'Pages',
                'label_singular' => 'Page',
                'routePrefix' => '', // Add this - empty string for root level
                'count' => true,
                'create' => true,
                'activity' => true,
                'search' => true,
                'search_fields' => ['title', 'content']
            ],
            'App\Models\Tip' => [
                'name' => 'tips',
                'label' => ucfirst(config('renewal.general.tips_prefix')) . ' Tips',
                'label_singular' => ucfirst(config('renewal.general.tips_prefix')) . ' Tip',
                'routePrefix' => 'content',
                'create' => true,
                'activity' => true,
                'search' => true,
                'search_fields' => ['title', 'description', 'summary'],
            ],
        ],
    ],

    'media_library' => [
        'disk' => 'libraries',
        'prefix_uuid_with_local_path' => true,
        'crops' => [
            'image' => [
                'original' => [
                    [
                        'name' => 'original',
                        'ratio' => 1,
                    ],
                ],
                'desktop' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 16 / 9,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
                'tablet' => [
                    [
                        'name' => 'tablet',
                        'ratio' => 4 / 3,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
                'mobile' => [
                    [
                        'name' => 'mobile',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'mobile_image' => [
                'original' => [
                    [
                        'name' => 'original',
                        'ratio' => 1,
                    ],
                ],
                'default' => [
                    [
                        'name' => 'default',
                        'ratio' => 16 / 9,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'desktop_image' => [
                'original' => [
                    [
                        'name' => 'original',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'recipe_image' => [
                'original' => [
                    [
                        'name' => 'original',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'promo_image' => [
                'desktop' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 16 / 9,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
                'tablet' => [
                    [
                        'name' => 'tablet',
                        'ratio' => 4 / 3,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
                'mobile' => [
                    [
                        'name' => 'mobile',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'affiliate_image' => [
                'desktop' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 16 / 9,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
                'tablet' => [
                    [
                        'name' => 'tablet',
                        'ratio' => 4 / 3,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
                'mobile' => [
                    [
                        'name' => 'mobile',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'slideshow' => [
                'desktop' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 16 / 9,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
                'tablet' => [
                    [
                        'name' => 'tablet',
                        'ratio' => 4 / 3,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
                'mobile' => [
                    [
                        'name' => 'mobile',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'cover' => [
                'desktop' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 16 / 9,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
                'tablet' => [
                    [
                        'name' => 'tablet',
                        'ratio' => 4 / 3,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
                'mobile' => [
                    [
                        'name' => 'mobile',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'share_image' => [
                'default' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 16 / 9,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ]
            ],
            'logo' => [
                'default' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 16 / 9,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ]
            ],
            'background_image' => [
                'original' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'tagline_image' => [
                'original' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'left_cta_background_image' => [
                'original' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
            'right_cta_background_image' => [
                'original' => [
                    [
                        'name' => 'desktop',
                        'ratio' => 1,
                        'minValues' => [
                            'width' => 100,
                            'height' => 100,
                        ],
                    ],
                ],
            ],
        ],
    ],

    'file_library' => [
        'disk' => 'file_libraries',
        'endpoint_type' => env('FILE_LIBRARY_ENDPOINT_TYPE', 's3'),
        'cascade_delete' => env('FILE_LIBRARY_CASCADE_DELETE', false),
        'local_path' => env('FILE_LIBRARY_LOCAL_PATH', 'uploads'),
        'file_service' => env('FILE_LIBRARY_FILE_SERVICE', 'A17\Twill\Services\FileLibrary\Disk'),
        'acl' => env('FILE_LIBRARY_ACL', 'public-read'),
        'filesize_limit' => env('FILE_LIBRARY_FILESIZE_LIMIT', 50),
        'allowed_extensions' => [],
        'prefix_uuid_with_local_path' => false,
    ],

    'buckets' => [
        'footerMenus' => [
            'name' => 'Footer Menus',
            'route_prefix' => 'settings',
            'buckets' => [
                'treatments_menu' => [
                    'name' => 'Treatments Menu',
                    'bucketables' => [
                        [
                            'module' => 'treatments',
                            'name' => 'Treatments',
                            'scopes' => ['published' => true],
                        ],
                    ],
                    'max_items' => 10,
                ],
                'conditions_menu' => [
                    'name' => 'Conditions Menu',
                    'bucketables' => [
                        [
                            'module' => 'conditions',
                            'name' => 'Conditions',
                            'scopes' => ['published' => true],
                        ],
                    ],
                    'max_items' => 10,
                ],
            ],
        ],
    ],

    'buckets_routes' => [
        'footerMenus' => 'settings'
    ],

    // 'admin_app_path' => '/admin',
    // 'admin_middleware_group' => 'admin',
    // 'auth_login_redirect_path' => '/admin',

    // 'pages' => [
    //     'enabled' => true,
    //     'content_table' => 'page_contents',
    // ],
];
