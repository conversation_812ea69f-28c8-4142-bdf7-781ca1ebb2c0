<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleBlocks;
use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleFiles;
use App\Repositories\GlobalRepository;
use App\Models\MinkiPost;

class MinkiPostRepository extends GlobalRepository
{
    use HandleBlocks, HandleSlugs, HandleMedias, HandleFiles;

    public function __construct(MinkiPost $model)
    {
        $this->model = $model;
    }
}
