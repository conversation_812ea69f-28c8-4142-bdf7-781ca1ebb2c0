<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;
use Route;

class StaffMemberController extends ModuleController
{
    protected $indexOptions = [
        'reorder' => true,
    ];

    protected $moduleName = 'staffMembers';

    protected $previewView = 'frontend.staffMembers.detail';

    protected $indexColumns = [
        'image' => [
            'thumb' => true, // image column
            'variant' => [
                'role' => 'cover',
                'crop' => 'default',
            ],
        ],
        'title' => [ // field column
            'title' => 'Title',
            'field' => 'title',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'slug' => [
            'title' => 'Slug',
            'field' => 'slug',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'type' => [ // relation column
            'title' => 'Type',
            'sort' => true,
            'relationship' => 'type',
            'field' => 'title'
        ],
    ];

    /*
     * Filters mapping ('filterName' => 'filterColumn')
     * You can associate items list to filters by having a filterNameList key in the indexData array
     * For example, 'category' => 'category_id' and 'categoryList' => app(CategoryRepository::class)->listAll()
     */
    protected $filters = [
        'type' => 'type'
    ];

    /*
     * Can be used in child classes to disable the content editor (full screen block editor)
     */
    protected $disableEditor = true;

    protected $perPage = 20;

    protected function previewData($item)
    {
        $staff_member_type = \App\Models\StaffMemberType::where('id', $item->type->id)->with('staff')->first();

        return [
            'content' => $item,
            'staff_member_type' => $staff_member_type
        ];
    }

    protected function indexData($request)
    {
        $staff_member_types = \App\Models\StaffMemberType::all()->map(function ($item) {
            return [
                'value' => $item->id,
                'label' => $item->title
            ];
        })->toArray();

        return [
            'typeList' => $staff_member_types
        ];
    }

    /*
     * Add anything you would like to have available in your module's form view
     * For example, relationship lists for multiselect form fields
     */
    protected function formData($request)
    {
        $staff_member_types = \App\Models\StaffMemberType::all()->map(function ($item) {
            return [
                'value' => $item->id,
                'label' => $item->title
            ];
        })->toArray();

        return ['staff_member_types' => $staff_member_types];
    }

    // TODO: Fix this so that the preview link at the top of the page works.

    // protected function form($id, $item = null): array
    // {
    //     $item = $item ?? $this->repository->getById($id, $this->formWith, $this->formWithCount);
    //     $fullRoutePrefix = 'admin.' . ($this->routePrefix ? $this->routePrefix . '.' : '') . $this->moduleName . '.';
    //     $previewRouteName = $fullRoutePrefix . 'preview';
    //     $restoreRouteName = $fullRoutePrefix . 'restoreRevision';

    //     $baseUrl = $item->urlWithoutSlug ?? $this->getPermalinkBaseUrl($item);

    //     $data = [
    //         'item' => $item,
    //         'moduleName' => $this->moduleName,
    //         'routePrefix' => $this->routePrefix,
    //         'titleFormKey' => $this->titleFormKey ?? $this->titleColumnKey,
    //         'translate' => $this->moduleHas('translations'),
    //         'permalink' => $this->getIndexOption('permalink'),
    //         'form_fields' => $this->repository->getFormFields($item),
    //         'baseUrl' => $baseUrl,
    //         'permalinkPrefix' => $this->getPermalinkPrefix($baseUrl),
    //         'saveUrl' => $this->getModuleRoute($item->id, 'update'),
    //         'editor' => $this->moduleHas('revisions') && $this->moduleHas('blocks') && !$this->disableEditor,
    //         'blockPreviewUrl' => route('twill.blocks.preview'),
    //         'revisions' => $this->moduleHas('revisions') ? $item->revisionsArray() : null,
    //     ] + (Route::has($previewRouteName) ? [
    //         'previewUrl' => moduleRoute($this->moduleName, $this->routePrefix, 'preview', $item->id),
    //     ] : [])
    //         + (Route::has($restoreRouteName) ? [
    //             'restoreUrl' => moduleRoute($this->moduleName, $this->routePrefix, 'restoreRevision', $item->id),
    //         ] : []);

    //     // dd($this->repository->getFormFields($item));
    //     // dd($this->formData($this->request), $data);

    //     return array_replace_recursive($data, $this->formData($this->request));
    // }

    public function getPermalinkBaseUrl($item = false)
    {
        if ($item && $item->type) {
            $this->permalinkBase = 'staff/' . $item->type->slug;
        } else {
            $this->permalinkBase = '';
        }

        return request()->getScheme() . '://' . config('app.url') . '/'
            . ($this->moduleHas('translations') ? '{language}/' : '')
            . ($this->moduleHas('revisions') ? '{preview}/' : '')
            . ($this->permalinkBase ?? $this->moduleName)
            . (isset($this->permalinkBase) && empty($this->permalinkBase) ? '' : '/');
    }
}
