<?php

namespace App\Providers;

use App\Models\Page;
use App\Models\ProductRange;
use App\Models\PackageCategory;
use App\Models\ConditionCategory;
use App\Models\TreatmentCategory;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\ServiceProvider;

class NavigationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot()
    {

        View::composer('frontend.partials.header', function ($view) {
            $conditions = Cache::remember('conditions_data', now()->addDays(30), function () {
                return ConditionCategory::published()->visible()
                    ->orderBy('position', 'asc')
                    ->with([
                        'conditions',
                        'conditions.slugs',
                        'sub_categories' => function ($query) {
                            $query->where('published', true)
                                ->orderBy('position', 'asc');
                        },
                        'sub_categories.conditions' => function ($query) {
                            $query->where('published', true)
                                ->orderBy('title', 'asc');
                        },
                        'sub_categories.conditions.slugs' => function ($query) {
                            $query->where('active', true);
                        }
                    ])
                    ->get()
                    ->toArray();
            });

            $treatments = Cache::remember('treatments_data', now()->addDays(30), function () {
                return TreatmentCategory::published()->visible()
                    ->orderBy('position', 'asc')
                    ->with([
                        'treatments',
                        'treatments.slugs',
                        'sub_categories' => function ($query) {
                            $query->where('published', true)
                                ->orderBy('position', 'asc');
                        },
                        'sub_categories.treatments' => function ($query) {
                            $query->where('published', true)
                                ->orderBy('title', 'asc');
                        },
                        'sub_categories.treatments.slugs' => function ($query) {
                            $query->where('active', true);
                        }
                    ])
                    ->get()
                    ->toArray();
            });

            $packages = Cache::remember('packages_data', now()->addDays(30), function () {
                return PackageCategory::published()->visible()
                    ->orderBy('position', 'asc')
                    ->with([
                        'slugs' => function ($query) {
                            $query->where('active', true);
                        },
                    ])
                    ->get()
                    ->toArray();
            });

            $about_links = Cache::remember('about_data', now()->addDays(30), function () {
                return config('renewal.about');
            });

            $products = Cache::remember('product_data', now()->addDays(30), function () {
                return ProductRange::published()->visible()
                    ->orderBy('position', 'asc')
                    ->with(['slugs' => function ($query) {
                        $query->where('active', true);
                    }])->get();
            });

            $memberships = Cache::remember('memberships_data', now()->addDays(30), function () {
                return Page::published()
                    ->visible()
                    ->whereHas('slug', function ($query) {
                        $query->where('slug', 'skin-renewal-elite-membership')->where('active', true);
                    })
                    ->with(['slug' => function ($query) {
                        $query->where('active', true);
                    }])
                    ->exists();
            });

            $view->with('products', $products);
            $view->with('about_links', $about_links);
            $view->with('packages', $packages);
            $view->with('conditions', $conditions);
            $view->with('treatments', $treatments);
            $view->with('memberships', $memberships);
        });
    }
}
