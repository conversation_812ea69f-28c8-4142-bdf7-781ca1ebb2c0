<?php

namespace App\Models\Behaviors;

use A17\Twill\Models\Behaviors\HasBlocks;

trait HasMyBlocks
{
	use HasBlocks;

	public function renderBlock($name) {
		$block = $this->blocks->where('type', $name)->where('parent_id', null)->first();
		if ($block != null) {
			$block->children = $this->blocks->where('parent_id', $block->id);
		}
		return view('frontend.blocks.' . $name, ['block' => $block])->render();
	}

	public function getBlock($name) {
		return $this->blocks->where('type', $name)->where('parent_id', null)->first();
	}

	public function getBlocks($renderChilds = true, $blockViewMappings = [], $data = [])
    {
		dd($this->blocks->where('parent_id', null));
		// $parents = $this->blocks->where('parent_id', null)->map(function ($block) use ($blockViewMappings, $renderChilds, $data) {
        //     if ($renderChilds) {
        //         $childBlocks = $this->blocks->where('parent_id', $block->id);

        //         $renderedChildViews = $childBlocks->map(function ($childBlock) use ($blockViewMappings, $data) {
        //             $view = $this->getBlockView($childBlock->type, $blockViewMappings);
        //             return view($view, $data)->with('block', $childBlock)->render();
        //         })->implode('');
        //     }

        //     $block->childs = $this->blocks->where('parent_id', $block->id);

        //     $view = $this->getBlockView($block->type, $blockViewMappings);

        //     return view($view, $data)->with('block', $block)->render() . ($renderedChildViews ?? '');
        // })->implode('');
    }
}