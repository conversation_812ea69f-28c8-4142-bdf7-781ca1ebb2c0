<?php

namespace App\Models;


use A17\Twill\Models\Model;

class Review extends Model 
{
    protected $fillable = [
        'published',
        'title',
        'description',
        "rating",
		"display_on_treatment_page",
		"display_on_branch_page",
		"display_author_name",
		"date_of_visit",
		"reviewer_contact_number",
		"reviewer_name",
		"reviewer_surname",
		"consent_to_publish",
		"display_on_conditions_page",
		"branch_id",
		"treatment_id",
		"condition_id",
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
		'published',
		'display_on_treatment_page',
		'display_on_branch_page',
		'display_on_conditions_page',
		'display_author_name',
		'consent_to_publish'
	];
	
	/**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'date_of_visit',
    ];

	public function branch() {
		return $this->belongsTo(\App\Models\Branch::class);
	}

	public function treatment() {
		return $this->belongsTo(\App\Models\Treatment::class);
	}
}
