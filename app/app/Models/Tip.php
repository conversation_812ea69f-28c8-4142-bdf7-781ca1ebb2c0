<?php

namespace App\Models;

use A17\Twill\Models\Behaviors\HasSlug;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasFiles;
use A17\Twill\Models\Behaviors\HasRevisions;
use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Behaviors\Sortable;
use A17\Twill\Models\Model;
use Laravel\Scout\Searchable;
use App\Models\Behaviors\HasSeo;

class Tip extends Model implements Sortable
{
    use HasSlug, HasMedias, HasFiles, HasRevisions, HasPosition, HasSeo, Searchable;

    protected $fillable = [
        'published',
        'title',
        'description',
        'summary'
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = [];
    
    // uncomment and modify this as needed if you use the HasSlug trait
    public $slugAttributes = [
        'title',
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
	];

	public function shouldBeSearchable()
	{
		return $this->published;
	}
	
	/**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
		$fullArray = $this->toArray();
		
		$fullArray = $this->transform($fullArray);

		// Customize array...
		return [
			'title'       => $fullArray['title'],
			//'description' => $fullArray['description'] ?? '',
			'summary'     => $fullArray['summary'] ?? ''
		];
    }
}
