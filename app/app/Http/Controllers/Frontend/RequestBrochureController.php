<?php

namespace App\Http\Controllers\Frontend;

//use Illuminate\Http\Request;
use App\Models\Page;
use App\Services\Emma;
use App\Models\Treatment;
use App\Mail\UserBrochureLinks;
use App\Models\RequestBrochure;
use App\Mail\AdminNotifications;
use App\Models\NewsletterSignup;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\View;
use App\Http\Requests\RequestBrochureForm;
use A17\Twill\Repositories\SettingRepository;

class RequestBrochureController extends Controller
{
    public function request_brochure($checked = false)
    {
        $page = Page::forSlug('request-brochure')->first();

        $checkbox_list = \App\Models\TreatmentCategory::with(['treatments' => function ($query) {
            $query->where('published', true)->has('files');
        }])->get()->filter(function ($item) {
            if ($item->treatments->count() > 0) {
                return true;
            }
            return false;
        });

        $conditions_checkbox_list = \App\Models\ConditionCategory::with(['conditions' => function ($query) {
            $query->has('files');
        }])->get()->filter(function ($item) {
            if ($item->conditions->count() > 0) {
                return true;
            }
            return false;
        });

        //Update SEO meta to include the treatment to prevent duplicates
        if ($checked) {
            $checked = ucwords(str_replace('-', ' ', $checked));
            $page->seo_meta->page_title = $page->seo_meta->page_title . ' | ' . $checked;
            $page->seo_meta->page_description = $page->seo_meta->page_description . ' - ' . $checked;
        }

        return view('frontend.pages.form', [
            'page'                     => $page,
            'checkbox_list'            => $checkbox_list,
            'conditions_checkbox_list' => $conditions_checkbox_list,
            'checked'                  => $checked
        ]);
    }

    public function request_brochure_thankyou($id)
    {
        $page = Page::forSlug('request-a-brochure-thankyou')->first();
        $submission = RequestBrochure::where('id', $id)->first();

        return view('frontend.pages.form', [
            'page'      => $page,
            'brochures' => $submission->brochures
        ]);
    }

    // public function store(RequestBrochureForm $request)
    // {
    //     $data = $request->validated();
    //     $data['published'] = true;
    //     $data['brochures'] = $request->brochures;


    //     $save_form = RequestBrochure::create($data);

    //     //Admin notification email
    //     $notification_email = app(SettingRepository::class)->byKey('notification_email', 'general');
    //     Mail::to($notification_email)->send(new AdminNotifications($save_form));

    //     //User notification email with download links
    //     Mail::to($data['email'])->send(new UserBrochureLinks($data));

    //     $redirect = $request->input('redirect') . '/' . $save_form->id ?? '/';
    //     return redirect($redirect);
    // }

    public function store(RequestBrochureForm $request)
    {
        // Access the shared global_settings
        $globalSettings = View::shared('global_settings');

        // Access the branch_list
        $branchList = $globalSettings['branch_list'];

        $data = $request->validated();
        $data['published'] = true;
        $data['brochures'] = $request->brochures;

        // Create a new array to store the modified brochures
        $modifiedBrochures = [];
        $treatmentsWithYouTube = [];

        // Update each brochure in the array to remove the part after the "|"
        foreach ($data['brochures'] as $brochure) {
            $position = strpos($brochure, '|');

            if ($position !== false) {
                $brochure = substr($brochure, 0, $position);
            }

            // Add the modified brochure to the new array
            $modifiedBrochures[] = $brochure;

            // Gets all the treatments with YouTube links to be passed into the email
            // Find treatment by title or ID (based on how it's stored)
            $treatment = Treatment::where('title', $brochure)->first();

            // Update this to only use the first YouTube link if available
            if ($treatment) {
                $treatmentsWithYouTube[] = [
                    'youtube_url' => $treatment->selectcast_embeds[0]['selectcast_share_link'] ?? null,
                ];
            }

            $youtubeLink = null;

            if ($treatment && !$youtubeLink) {  // Only set if not already set
                // Check if the treatment has a YouTube link
                $youtubeLink = $treatment->selectcast_embeds[0]['selectcast_share_link'] ?? null;
                if ($youtubeLink) break;  // Exit loop after finding first link
            }
        }

        $data['brochure_titles'] = $modifiedBrochures;
        $data['treatments_with_youtube'] = $treatmentsWithYouTube;
        $data['first_youtube_url'] = $youtubeLink;

        // Log the new array with just the names
        // Log::info($modifiedBrochures);
        // Log::info($request->brochures);

        // Get the branch with the matching ID
        $matchedBranch = $branchList->first(function ($branch) use ($data) {
            return $branch->id == $data['branch_id'];
        });

        // Add branch information to $data
        $data['branch_name'] = $matchedBranch ? $matchedBranch->title : null;

        // Check if the checkbox is checked
        $subscribeFromBrochure = $request->has('group') && isset($request->group[4459202]);

        // Check if the checkbox is checked
        if ($subscribeFromBrochure && $matchedBranch) {
            $emma = new Emma;

            // Emma formatted data array
            $emma_signup_data = [
                '_token' => $request->input('_token'), // We need to include the CSRF token.
                'fields' => [
                    'first_name' => $data['name'],
                    'last_name' => 'none',
                    'preferred-branch' => $matchedBranch->title,
                ],
                'email' => $data['email'],
                'group' => $data['group'],
            ];

            $newsletter_signup = $emma->signup($emma_signup_data);

            $form_data = $emma_signup_data['fields'];
            $form_data['groups'] = $data['group'];
            $form_data['signup_response'] = $newsletter_signup->member_id;

            $newsletter_subscription = new NewsletterSignup;
            $newsletter_subscription->email = $data['email'];
            $newsletter_subscription->data = $form_data;
            $newsletter_subscription->published = 1;
            $newsletter_subscription->save();

            // Admin notification email
            $notification_email = app(SettingRepository::class)->byKey('notification_email', 'general');
            Mail::to($notification_email)->send(new AdminNotifications($newsletter_subscription, $subscribeFromBrochure));
        } elseif ($subscribeFromBrochure) {
            return "No branch found with ID {$data['branch_id']}.";
        }

        $save_form = RequestBrochure::create($data);

        // Admin notification email
        // $notification_email = app(SettingRepository::class)->byKey('notification_email', 'general');
        $notification_email = '<EMAIL>';
        Mail::to($notification_email)->send(new AdminNotifications($save_form));

        // User notification email with download links
        Mail::to($data['email'])->send(new UserBrochureLinks($data));

        // Send data to webhook
        $webhookUrl = 'https://backend.leadconnectorhq.com/hooks/D6ZXUYuh5hOKSs003cRh/webhook-trigger/7a485d23-4a28-427b-84f3-ec6e9ee0150a';
        $response = Http::post($webhookUrl, $data);

        if (!$response->successful()) {
            // Handle the error
            Log::info('Webhook was not sent', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
        } else {
            Log::info($data);
        }

        $redirect = $request->input('redirect') . '/' . $save_form->id ?? '/';
        return redirect($redirect);
    }
}
