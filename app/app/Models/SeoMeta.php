<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SeoMeta extends Model
{
	use SoftDeletes;
	
    protected $fillable = [
		'id',
		'page_title',
		'page_description',
		'crawl_priority',
		'change_frequency',
		'show_on_sitemap',
	];

	public function getChangeFreqAttribute()
	{
		$map = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'];
		return $map[$this->change_frequency - 1];
	}
}
