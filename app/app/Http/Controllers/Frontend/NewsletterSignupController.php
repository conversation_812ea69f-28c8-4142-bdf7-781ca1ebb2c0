<?php

namespace App\Http\Controllers\Frontend;

use App\Services\Emma;
use Illuminate\Http\Request;
use App\Mail\AdminNotifications;
use App\Models\NewsletterSignup;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use A17\Twill\Repositories\SettingRepository;

class NewsletterSignupController extends Controller
{
    public function store(Request $request)
    {

        $this->validate($request, [
            'fields.first_name'       => 'required',
            'fields.last_name'        => 'required',
            'email'                   => 'required|email',
            'fields.preferred-branch' => 'required',
            'group'                   => 'required',
			'g-recaptcha-response' 	  => 'recaptcha',
        ]);

        $emma = new Emma;

        $data = $request->all();

        $signup = $emma->signup($data);

        // dd($signup->member_id);

        //Handle the amp form submission
        $form_data = $data['fields'];
        $form_data['groups'] = $data['group'];
        $form_data['signup_response'] = $signup->member_id;

        $newsletter_subscription = new NewsletterSignup;
        $newsletter_subscription->email = $data['email'];
        $newsletter_subscription->data = $form_data;
        $newsletter_subscription->published = 1;
        $newsletter_subscription->save();

        //Admin notification email
        $notification_email = app(SettingRepository::class)->byKey('notification_email', 'general');
        Mail::to($notification_email)->send(new AdminNotifications($newsletter_subscription));  // TODO: Might need to comment this back out

        $webhookUrl = "https://services.leadconnectorhq.com/hooks/D6ZXUYuh5hOKSs003cRh/webhook-trigger/27319219-0ac1-4d20-875a-2a5e6b5a5794";

        $webhookData = $data['fields'];
        $webhookData['email'] = $data['email'];

        $response = Http::post($webhookUrl, $webhookData);

        if (!$response->successful()) {
            // Handle the error and log the reason for failure
            Log::info('Webhook was not sent', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
        } else {
            // Log::info($webhookData);
        }



        $redirect = $request->input('redirect') ?? '/';
        return redirect($redirect);
    }
}
