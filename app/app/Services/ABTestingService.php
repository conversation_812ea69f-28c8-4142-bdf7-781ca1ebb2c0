<?php

// app/Services/ABTestingService.php
namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class ABTestingService
{
    const BANNER_A_COUNTER = 'banner_test:variant_a:impressions';
    const BANNER_B_COUNTER = 'banner_test:variant_b:impressions';

    public function getBannerVariant()
    {
        // Get or generate visitor ID from session
        $visitorId = session()->get('visitor_id');
        if (!$visitorId) {
            $visitorId = uniqid();
            session()->put('visitor_id', $visitorId);
        }

        // Check if this visitor has already been assigned a variant
        $variantKey = 'banner_test:visitor:' . $visitorId;
        $variant = Redis::get($variantKey);

        if (!$variant) {
            // New visitor - assign variant and increment counter
            $hash = crc32($visitorId);
            $variant = ($hash % 2 === 0) ? 'A' : 'B';
            
            // Store the visitor's variant for 30 days
            Redis::setex($variantKey, 60 * 60 * 24 * 30, $variant);
            
            // Increment the appropriate counter
            $counterKey = $variant === 'A' ? self::BANNER_A_COUNTER : self::BANNER_B_COUNTER;
            Redis::incr($counterKey);
        }

        return $variant;
    }

	public function getStats()
    {
        return [
            'variant_a' => (int) Redis::get(self::BANNER_A_COUNTER) ?? 0,
            'variant_b' => (int) Redis::get(self::BANNER_B_COUNTER) ?? 0,
        ];
    }

}