<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleBlocks;
use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleFiles;
use A17\Twill\Repositories\Behaviors\HandleRevisions;
use App\Repositories\GlobalRepository;
use App\Models\StaffMember;

class StaffMemberRepository extends GlobalRepository
{
    use HandleBlocks, HandleSlugs, HandleMedias, HandleFiles, HandleRevisions;

    public function __construct(StaffMember $model)
    {
        $this->model = $model;
	}

	// implement the filter method
	public function filter(\Illuminate\Database\Eloquent\Builder $query, array $scopes = []): \Illuminate\Database\Eloquent\Builder {

		if (isset($scopes['type'])) {
			$query->whereHas('type', function ($query) use ($scopes) {
				$query->where('id', $scopes['type']);
			});
			unset($scopes['type']);
		}

		return parent::filter($query, $scopes);
	}
}
