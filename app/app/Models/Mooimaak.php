<?php

namespace App\Models;

use A17\Twill\Models\Behaviors\HasSlug;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasRevisions;
use A17\Twill\Models\Behaviors\HasPosition;
use App\Models\Behaviors\HasSeo;
use A17\Twill\Models\Behaviors\Sortable;
use A17\Twill\Models\Model;

class Mooimaak extends Model implements Sortable
{
    use HasSlug, HasMedias, HasRevisions, HasPosition, HasSeo;

    protected $fillable = [
        'published',
        'title',
        'excerpt',
        'first_name',
        'last_name',
        'episode_number',
        'about_contestant',
        'before_after_quote',
        'treatments_description',
        'contestant_quote',
        'treatments',
        'team',
        'position',
        'season',
        'publish_start_date',
        'publish_end_date',
        'seo_meta_id',
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = [];

    //Return JSON data as an array
    protected $casts = [
        'treatments' => 'array',
        'team' => 'array'
    ];

    // uncomment and modify this as needed if you use the HasSlug trait
    public $slugAttributes = [
        'title',
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
    ];

    // uncomment and modify this as needed if you use the HasMedias trait
    public $mediasParams = [
        'listing_before_pic' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ]
        ],
        'listing_after_pic' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ]
        ],
        'banner_pic_1' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ]
        ],
        'banner_pic_2' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ]
        ],
        'detail_before_pic' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ]
        ],
        'detail_after_pic' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ]
        ],
        'eyes_pic' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ]
        ],
    ];
}
