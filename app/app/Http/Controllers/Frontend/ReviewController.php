<?php

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\Condition;
use App\Models\Review;
use App\Models\Treatment;

class ReviewController extends Controller
{
    public function index(Request $request)
    {
        $branches = \App\Models\Branch::has('reviews')->distinct()->get();
        $treatments = \App\Models\Treatment::has('reviews')->distinct()->get();
        $page = \App\Models\Page::forSlug('reviews')->published()->visible()->first();

        if (!$page) {
            return abort(404);
        }

        $filtered_branch_id = null;
        $filtered_treatment_id = null;
        $filtered_branch = null;
        $filtered_treament = null;



        $reviews = new \App\Models\Review;
        $reviews = $reviews->published();

        if ($request->has('treatment') && $request->treatment != null) {
            $reviews = $reviews->where('treatment_id', $request->treatment);
            $filtered_treatment_id = $request->treatment;
            $filtered_treament = \App\Models\Treatment::has('reviews')->distinct()->find($filtered_treatment_id);

            if (!$filtered_treament) {
                return abort(404);
            }

            //Update SEO meta to include the treatment to prevent duplicates
            $page->seo_meta->page_title = $page->seo_meta->page_title . ' | ' . $filtered_treament->title;
            $page->seo_meta->page_description = $page->seo_meta->page_description . ' | ' . $filtered_treament->title;
        }

        if ($request->has('branch') && $request->branch != null) {
            $reviews = $reviews->where('branch_id', $request->branch);
            $filtered_branch_id = $request->branch;
            $filtered_branch = \App\Models\Branch::has('reviews')->distinct()->find($filtered_branch_id);

            if (!$filtered_branch) {
                return abort(404);
            }

            //Update SEO meta to include the treatment to prevent duplicates
            $page->seo_meta->page_title =  'Reviews for ' . $filtered_branch->title;
            $page->seo_meta->page_description = 'See our reviews for treatments done by ' . $filtered_branch->title;
        }

        $reviews = $reviews->orderBy('created_at', 'desc')->paginate(10);

        //Add page number to SEO meta to prevent duplicates
        if ($request->has('page')) {
            $page->seo_meta->page_title =  $page->seo_meta->page_title . ' | Page ' . $reviews->currentPage();
            $page->seo_meta->page_description = $page->seo_meta->page_description . ' | Page ' . $reviews->currentPage();
        }


        return view('frontend.pages.reviews', [
            'page'               => $page,
            'treatments'         => $treatments,
            'branches'           => $branches,
            'reviews'            => $reviews,
            'filtered_treatment' => $filtered_treament,
            'filtered_branch' => $filtered_branch
        ]);
    }

    public function detail($id)
    {
        $review = Review::where('id', $id)->visible()->first();
        if (!$review) {
            return abort(404);
        }

        return view('frontend.reviews.detail', [
            'review' => $review,
        ]);
    }

    public function add()
    {
        $branches = Branch::published()->get();
        $treatments = Treatment::published()->get();
        $conditions = Condition::published()->get();

        $page = \App\Models\Page::forSlug('reviews')->published()->visible()->first();
        $page->seo_meta->page_title = 'Add a review | Skin Renewal';
        $page->seo_meta->page_description = 'Submit your review for your favourite treatments and products.';

        return view('frontend.reviews.add', [
            'page' => $page,
            'branches' => $branches,
            'treatments' => $treatments,
            'conditions' => $conditions,
        ]);
    }

    public function store(Request $request)
    {

        // Simple honeypot to help prevent spam.
        if ($request->filled('try_me')) {
            return redirect()->back()->with('error', 'Invalid Submission');
        }

        $this->validate($request, [
            'reviewer_name' => 'required',
            'reviewer_surname' => 'required',
            'title' => 'required',
            'description' => 'required',
            'rating' => 'required',
            'date_of_visit' => 'required',
        ]);

        $data = $request->except('_token', 'try_me');
        $data['display_on_treatment_page'] = 1;
        $data['display_on_branch_page'] = 1;
        $data['display_on_conditions_page'] = 1;
        $data['display_author_name'] = 1;
        $data['consent_to_publish'] = 1;

        Review::create($data);

        return redirect()->back()->with('success', 'Review added');
    }
}
