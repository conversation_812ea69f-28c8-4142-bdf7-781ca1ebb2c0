<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

//use Illuminate\Contracts\Queue\ShouldQueue;

class AdminNotifications extends Mailable
{
    use Queueable, SerializesModels;

    public $submission;
    public $type;
    public $subscribeFromBrochure;
	private $cvPath;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($submission, $subscribeFromBrochure = false, $cvPath = null)
    {
        $this->type = class_basename($submission);
        $this->submission = $submission->toArray();
        if (isset($this->submission['branch_id'])) {
            $this->submission['branch'] = \App\Models\Branch::where('id', $this->submission['branch_id'])->pluck('title')[0];
        }
        $this->submission = Arr::except($this->submission, ['updated_at', 'deleted_at', 'published', 'title', 'branch_id']);
        $this->subscribeFromBrochure = $subscribeFromBrochure;
		$this->cvPath = $cvPath;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
		$mail = $this->markdown('emails.adminNotifications.index')->with('subscribeFromBrochure', $this->subscribeFromBrochure);

		if ($this->cvPath && file_exists($this->cvPath)) {
            $mail->attach($this->cvPath, [
                'as' => $this->submission['name'] . '-' . $this->submission['surname'] . '-CV.pdf',
                'mime' => 'application/pdf'
            ]);
        }
        return ;
    }
}
