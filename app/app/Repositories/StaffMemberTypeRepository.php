<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleMedias;
//use A17\Twill\Repositories\Behaviors\HandleRevisions;
use App\Repositories\GlobalRepository;
use App\Models\StaffMemberType;

class StaffMemberTypeRepository extends GlobalRepository
{
    use HandleSlugs, HandleMedias;

    public function __construct(StaffMemberType $model)
    {
        $this->model = $model;
    }
}
