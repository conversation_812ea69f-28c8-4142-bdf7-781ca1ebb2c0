<?php

namespace App\Http\Controllers\Frontend;

//use Illuminate\Http\Request;
use App\Models\AskDoctor;
use App\Mail\AdminNotifications;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use App\Http\Requests\SaveAskDoctorForm;
use A17\Twill\Repositories\SettingRepository;

class AskDoctorController extends Controller
{
    public function store(SaveAskDoctorForm $request)
    {
        $data = $request->validated();
        $data['title'] = $data['name'] . ' ' . $data['surname'];
        $data['published'] = true;

        $ask_doctor = AskDoctor::create($data);

        //Admin notification email
        $notification_email = app(SettingRepository::class)->byKey('notification_email', 'general');
        Mail::to($notification_email)->send(new AdminNotifications($ask_doctor));

        // Send data to webhook
        $webhookUrl = 'https://backend.leadconnectorhq.com/hooks/D6ZXUYuh5hOKSs003cRh/webhook-trigger/3fa0293d-c3e7-482e-b1e1-42ab503da5f9';
        $response = Http::post($webhookUrl, $data);

        if (!$response->successful()) {
            // Handle the error and log the reason for failure
            Log::info('Webhook was not sent', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
        } else {
            // Log::info($data);
        }

        $redirect = $request->input('redirect') ?? '/';
        return redirect($redirect);
    }
}
