<?php

namespace App\Models;

use A17\Twill\Models\Behaviors\HasBlocks;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasFiles;
use A17\Twill\Models\Behaviors\HasRevisions;
// use A17\Twill\Models\Behaviors\HasPosition;
// use A17\Twill\Models\Behaviors\Sortable;
use A17\Twill\Models\Model;
use Laravel\Scout\Searchable;

class Product extends Model
{
    use HasBlocks, HasMedias, HasFiles, HasRevisions, Searchable;

    protected $fillable = [
        'published',
        'title',
        'description',
        'rrp',
		'volume',
		'buy_now_url',
        'seo_meta_id',
        'product_range_id',
        'position',
        'publish_start_date',
        'publish_end_date',
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['range'];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
    ];

    // uncomment and modify this as needed if you use the HasMedias trait
    public $mediasParams = [
        'cover' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ],
            'mobile' => [
                [
                    'name' => 'mobile',
                    'ratio' => 1,
                ],
            ],
        ],
	];
	
	public function range()
    {
        return $this->belongsTo(\App\Models\ProductRange::class, 'product_range_id');
	}
	/**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
		$fullArray = $this->toArray();
		
		$fullArray = $this->transform($fullArray);

		// Customize array...
		return [
			'title'       => $fullArray['title'],
			'description' => $fullArray['description'] ?? '',
			'range'       => $this->range->title ?? ''
		];
    }

    public function shouldBeSearchable()
    {
        return $this->range != null && $this->published;
    }
}
