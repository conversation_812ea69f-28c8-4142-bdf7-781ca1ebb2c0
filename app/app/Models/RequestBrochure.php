<?php

namespace App\Models;


use A17\Twill\Models\Model;

class RequestBrochure extends Model 
{
	protected $presenterAdmin = \App\Models\Presenters\FormsPresenter::class;

    protected $fillable = [
        'name',
		'email',
		'phone_number',
		'published',
		'branch_id',
		'brochures',
	];
	
	protected $casts = [
		'brochures' => 'array'
	];

	public function branch() {
		return $this->belongsTo('\App\Models\Branch');
	}
}
