<?php

namespace App\Models;

use A17\Twill\Models\Behaviors\HasBlocks;
use A17\Twill\Models\Behaviors\HasSlug;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasFiles;
use A17\Twill\Models\Behaviors\HasRevisions;
use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Behaviors\Sortable;
use App\Models\Behaviors\HasSeo;
use A17\Twill\Models\Model;
use Laravel\Scout\Searchable;

class Condition extends Model implements Sortable
{
    use HasBlocks, HasSlug, HasMedias, HasFiles, HasRevisions, HasPosition, HasSeo, Searchable;

    protected $fillable = [
        'published',
        'title',
        'description',
        'position',
        'conditions_featured_text',
		'conditions_content',
		'conditions_video',
		'conditions_questions_and_answers',
        'featured',
        'publish_start_date',
        'publish_end_date',
        'condition_category_id',
        'selectcast_embeds',
        'two_column_content',
        'tip',
		'condition_sub_category_id'
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['reviews'];

    // uncomment and modify this as needed if you use the HasSlug trait
    public $slugAttributes = [
        'title',
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
	];

	//Return JSON data as an array
	protected $casts = [
		'conditions_questions_and_answers' => 'array',
		'selectcast_embeds' => 'array',
	];

	public $filesParams = ['brochure'];

    // uncomment and modify this as needed if you use the HasMedias trait
    public $mediasParams = [
        'slideshow' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ],
            'mobile' => [
                [
                    'name' => 'mobile',
                    'ratio' => 1,
                ],
            ],
        ],
        'condition_image' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ],
            'mobile' => [
                [
                    'name' => 'mobile',
                    'ratio' => 1,
                ],
            ],
        ],
	];

	public function treatments()
    {
        return $this->belongsToMany(\App\Models\Treatment::class);
	}

	public function package_categories()
    {
        return $this->belongsToMany(\App\Models\PackageCategory::class);
	}

	public function category() {
		return $this->belongsTo(\App\Models\ConditionCategory::class, 'condition_category_id');
	}

	public function sub_category() {
		return $this->belongsTo(\App\Models\ConditionSubCategory::class, 'condition_sub_category_id');
	}

	public function reviews() {
		return $this->hasMany(\App\Models\Review::class)->orderBy('date_of_visit', 'desc');
	}

	/**
     * Splits the given value.
     *
     * @param  string $value
     * @return array
     */
    public function splitConditionsQuestionsAndAnswers($value)
    {
        return $value;
	}

	public function shouldBeSearchable()
	{
		return $this->published;
	}

	/**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
		$fullArray = $this->toArray();

		$fullArray = $this->transform($fullArray);

		// Customize array...
		return [
			'title'                            => $fullArray['title'],
			'treatments_featured_text'         => $fullArray['conditions_featured_text'] ?? '',
			'treatments_content'               => isset($fullArray['conditions_content']) ? strip_tags($fullArray['conditions_content']) : '',
			//'treatments_questions_and_answers' => $fullArray['conditions_questions_and_answers'],
			'category'                         => $this->category->title ??''
		];
    }

    /**
     * Get the latest posts from wordpress Skin Online Blog
     *
     * @return array
     */

     
}
