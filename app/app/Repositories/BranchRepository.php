<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleBlocks;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleRevisions;
use A17\Twill\Repositories\Behaviors\HandleRepeaters;
use App\Repositories\GlobalRepository;
use App\Models\Branch;
use function GuzzleHttp\json_decode;
use Illuminate\Support\Facades\DB;

//use function Aws\serialize;

class BranchRepository extends GlobalRepository
{
    use HandleBlocks, HandleSlugs, HandleMedias, HandleRepeaters, HandleRevisions;

    public function __construct(Branch $model)
    {
        $this->model = $model;
    }

    public function prepareFieldsBeforeSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): array
    {

        $count = 1;
        if (isset($fields['browsers'])) {
            foreach ($fields['browsers']['staff'] as $key => $staff) {

                DB::table('branch_staff_member')
                    ->where(
                        [
                            ['branch_id', '=', $object->id],
                            ['staff_member_id', '=', $staff['id']]
                        ]
                    )
                    ->update(['position' => $count]);

                $count++;
            }

            $count = 1;
            foreach ($fields['browsers']['product_ranges'] as $key => $product) {

                DB::table('branch_product_range')
                    ->where(
                        [
                            ['branch_id', '=', $object->id],
                            ['product_range_id', '=', $product['id']]
                        ]
                    )
                    ->update(['position' => $count]);

                $count++;
            }
        }

        if (isset($fields['repeaters']['addressline'])) {
            $fields['address'] = $fields['repeaters']['addressline'];
        }

        if (isset($fields['repeaters']['branch_packages'])) {
            $fields['packages'] = $fields['repeaters']['branch_packages'];
        }

        return parent::prepareFieldsBeforeSave($object, $fields);
    }

    public function orderStaff($staff, $branch)
    {

        $db_staff = DB::table('branch_staff_member')
            ->where(
                [
                    ['branch_id', '=', $branch],
                ]
            )->orderBy('position', 'ASC')
            ->get();

        $return = [];
        foreach ($db_staff as $db_staff_member) {
            foreach ($staff as $staff_member) {

                if ($db_staff_member->staff_member_id == $staff_member['id']) {
                    $return[] = $staff_member;
                    break;
                }
            }
        }

        return $return;
    }

    public function orderProducts($products, $branch)
    {

        $db_product = DB::table('branch_product_range')
            ->where(
                [
                    ['branch_id', '=', $branch],
                ]
            )->orderBy('position', 'ASC')
            ->get();

        $return = [];
        foreach ($db_product as $db_product) {
            foreach ($products as $product) {

                if ($db_product->product_range_id == $product['id']) {

                    $return[] = $product;
                    break;
                }
            }
        }

        return $return;
    }

    public function getFormFields(\A17\Twill\Models\Contracts\TwillModelContract $object): array
    {
        $fields = parent::getFormFields($object);

        //Q and A repeater
        if (isset($fields['address']) && !empty($fields['address'])) {
            $fields = $this->getJsonRepeater($fields, 'addressline', 'address');
        }

        if (isset($fields['packages']) && !empty($fields['packages'])) {
            $fields = $this->getJsonRepeater($fields, 'branch_packages', 'packages');
        }

        //related treatments
        $fields['browsers']['treatments'] = $this->getFormFieldsForBrowser($object, 'treatments', 'content');
        //related products
        $fields['browsers']['product_ranges'] = $this->getFormFieldsForBrowser($object, 'product_ranges', 'categories', 'title', 'productRanges');
        $fields['browsers']['product_ranges'] = $this->orderProducts($fields['browsers']['product_ranges'], $object->id);

        //staff members
        $fields['browsers']['staff'] = $this->getFormFieldsForBrowser($object, 'staff', 'content', 'title', 'staffMembers');
        $fields['browsers']['staff'] = $this->orderStaff($fields['browsers']['staff'], $object->id);
        return $fields;
    }

    public function afterSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): void
    {
        //dd($fields);
        $this->updateBrowser($object, $fields, 'treatments');
        $this->updateBrowser($object, $fields, 'staff');
        $this->updateBrowser($object, $fields, 'product_ranges');
        // $this->updateMultiSelect($object, $fields, 'package_categories');
        parent::afterSave($object, $fields);
    }

    // implement the filter method
    public function filter(\Illuminate\Database\Eloquent\Builder $query, array $scopes = []): \Illuminate\Database\Eloquent\Builder
    {
        //dd($scopes);
        // $scopes = [
        // 	'province' => 'province'
        // ];
        // add a where like clause
        //$this->addLikeFilterScope($query, $scopes, 'province');

        // don't forget to call the parent filter function
        return parent::filter($query, $scopes);
    }
}
