<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;

class AskDoctorController extends ModuleController
{
	protected $moduleName = 'askDoctors';

	/*
     * Can be used in child classes to disable the content editor (full screen block editor)
     */
    protected $disableEditor = true;
	
	/*
     * Options of the index view
     */
    protected $indexOptions = [
        'create' => false,
        'edit' => false,
        // 'publish' => false,
        // 'bulkPublish' => false,
        // 'feature' => false,
        // 'bulkFeature' => false,
        // 'restore' => true,
        // 'bulkRestore' => true,
        'delete' => true,
        // 'bulkDelete' => true,
        // 'reorder' => false,
        // 'permalink' => false,
        // 'bulkEdit' => false,
        // 'editInModal' => false,
	];
	
	/*
     * Available columns of the index view
     */
    protected $indexColumns = [
        'title' => [ // field column
            'title' => 'Name',
			'field' => 'title',
			'sort'  => true
        ],
        'email' => [
            'title' => 'Email',
            'field' => 'email',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
		],
		'phone_number' => [
            'title' => 'Phone Numner',
            'field' => 'phone_number',
            'sort' => false, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'how_did_you_hear' => [
            'title' => 'How did you hear',
            'field' => 'how_did_you_hear',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'branch' => [ // relation column
            'title' => 'Closest Branch',
            'sort' => true,
            'relationship' => 'branch',
			'field' => 'title',
			'visible' => true,
		],
		'questions' => [
            'title' => 'Question',
            'field' => 'questions',
            'sort' => false, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'created_at' => [ // presenter column
            'title' => 'Date',
            'field' => 'date',
            'present' => true,
            'visible' => true,
        ]
	];
}
