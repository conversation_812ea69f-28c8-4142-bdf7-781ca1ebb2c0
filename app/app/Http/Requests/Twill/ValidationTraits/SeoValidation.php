<?php

namespace App\Http\Requests\Twill\ValidationTraits;

trait SeoValidation {

	private $seo_rules;

	private function get_seo_rules($id) {
		if (!empty($id)) {
			$id = $id[0];
			return [
				'seo_meta-page_title' => [
					'required', 
					'max:65', 
					'unique:seo_metas,page_title,' . $id
				],
				'seo_meta-page_description' => [
					'required', 
					'max:160', 
					'unique:seo_metas,page_description,' . $id
				],
				'seo_meta-page_keywords' => [
					'nullable',
					'max:160', 
					'unique:seo_metas,page_keywords,' . $id
				],
			];
		}

		return [];
	}
}