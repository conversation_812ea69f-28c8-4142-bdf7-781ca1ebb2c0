<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleBlocks;
use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleFiles;
use A17\Twill\Repositories\Behaviors\HandleRevisions;
use App\Repositories\GlobalRepository;
use App\Models\Page;

class PageRepository extends GlobalRepository
{
    use HandleBlocks, HandleSlugs, HandleMedias, HandleFiles, HandleRevisions;

    public function __construct(Page $model)
    {
        $this->model = $model;
	}
	
	// public function get($with = [], $scopes = [], $orders = [], $perPage = 20, $forcePagination = false)
    // {
    //     $query = $this->model->with($with);

    //     $query = $this->filter($query, $scopes);
    //     $query = $this->order($query, $orders);

    //     if (!$forcePagination && $this->model instanceof Sortable) {
    //         return $query->ordered()->get();
    //     }

    //     if ($perPage == -1) {
    //         return $query->get();
	// 	}
		
	// 	//$query = $query->where('id', '!=', '1');

    //     return $query->paginate($perPage);
	// }
	
	public function prepareFieldsBeforeSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): array
    {
        return parent::prepareFieldsBeforeSave($object, $fields);
	}
}
