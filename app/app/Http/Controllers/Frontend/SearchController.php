<?php

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class SearchController extends Controller
{
    public function index(Request $request)
    {
        if (!$request->has('s')) {
            return redirect()->route('home');
        }

        $search_string = $request->s;
        $results = \App\Search\Sitewide::search($search_string)->get();

        //ensure no unpublished results are returned
        $results = $results->filter(function ($item, $key) {
            return $item->published == true;
        });

        foreach ($results as $index => $result) {
            $content_type = class_basename($result);
            $results[$index]->content_type = $content_type;

            //Format prefixes to make front end linking easier
            switch ($content_type) {
                case 'Tip':
                    $results[$index]->prefix = config('renewal.general.tips_prefix') . '-tips' . '/';
                    break;
                case 'Product':
                    if ($result->range != null) {
                        if ($result->range->external_url != null) {
                            //dd($result->range->external_url);
                            $results[$index]->url = $result->range->external_url;
                        } else {
                            $results[$index]->url = url($result->range->getSlug() . '/');
                        }
                    }
                    //If product has no range set, remove it from results
                    else {
                        unset($results[$index]);
                    }
                    break;
                case 'StaffMember':
                    $results[$index]->url = url('staff/' . $result->type->getSlug() . '/' . $result->getSlug());
                    break;
                default:
                    if ($result->getSlug() == 'mooimaak-season-1') {
                        $results[$index]->url = 'mooimaak/1';
                    } elseif ($result->getSlug() == 'mooimaak-season-two') {
                        $results[$index]->url = 'mooimaak/2';
                    } elseif ($result->getSlug() == 'branches') {
                        $results[$index]->url = '/sections/branches';
                    } elseif ($result->getSlug() == 'treatments') {
                        $results[$index]->url = '/sections/treatments';
                    } elseif ($result->getSlug() == 'conditions') {
                        $results[$index]->url = '/sections/conditions';
                    } else {
                        $results[$index]->url = url($result->getSlug());
                    }
                    break;
            }
        }

        $results = $results->values();
        //dd($results);
        if ($request->isMethod('post')) {
            return \Response::json($results, 201); // Status code here
        }

        //Update SEO meta to include the treatment to prevent duplicates
        $page = new \stdClass();
        $page->seo_meta = new \stdClass();
        $page->seo_meta->page_title = 'Search "' . $search_string . '"';
        $page->seo_meta->page_description = 'Search results for "' . $search_string . '"';

        return view('frontend.search.index', [
            'term'    => $search_string,
            'results' => $results,
            'page'      => $page
        ]);
    }
}
