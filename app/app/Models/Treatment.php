<?php

namespace App\Models;

use A17\Twill\Models\Behaviors\HasBlocks;
use A17\Twill\Models\Behaviors\HasSlug;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasFiles;
use A17\Twill\Models\Behaviors\HasRevisions;
use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Behaviors\Sortable;
use App\Models\Behaviors\HasSeo;
use A17\Twill\Models\Model;
use Laravel\Scout\Searchable;

class Treatment extends Model implements Sortable
{
    use HasBlocks, HasSlug, HasMedias, HasFiles, HasRevisions, HasPosition, HasSeo, Searchable;

    protected $fillable = [
        'published',
        'title',
		'treatments_featured_text',
		'treatments_content',
		'treatments_video',
		'position',
		'treatments_questions_and_answers',
        'publish_start_date',
        'publish_end_date',
        'treatment_category_id',
        'treatments_video',
        'icons_best_results',
        'icons_duration_of_results',
        'icons_cost',
        'icons_treatment_recovery',
        'icons_risks_and_complications',
        'icons_anaesthetic',
        'icons_procedure_time',
        'icons_skin_specialist',
        'icons_back_to_work',
        'related_packages',
        'selectcast_embeds',
        'two_column_content',
        'tip',
		'treatment_sub_category_id'
    ];
    
    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['reviews'];
    
    // uncomment and modify this as needed if you use the HasSlug trait
    public $slugAttributes = [
        'title',
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
	];

	//Return JSON data as an array
	protected $casts = [
        'treatments_questions_and_answers' => 'array',
        'related_packages' => 'array',
        'selectcast_embeds' => 'array',
    ];
	
	public $filesParams = ['brochure'];

    // uncomment and modify this as needed if you use the HasMedias trait
    public $mediasParams = [
        'slideshow' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ],
            'mobile' => [
                [
                    'name' => 'mobile',
                    'ratio' => 1,
                ],
            ],
        ],
        'treatment_image' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ],
            'mobile' => [
                [
                    'name' => 'mobile',
                    'ratio' => 1,
                ],
            ],
        ],
	];

	public function branches()
    {
        return $this->belongsToMany(\App\Models\Branch::class);
	}
	
	public function conditions()
    {
        return $this->belongsToMany(\App\Models\Condition::class);
	}
	
	public function category() {
		return $this->belongsTo(\App\Models\TreatmentCategory::class, 'treatment_category_id');
	}

	public function sub_category() {
		return $this->belongsTo(TreatmentSubCategory::class, 'treatment_sub_category_id');
	}

	public function reviews() {
		return $this->hasMany(\App\Models\Review::class);
    }
    
    public function package_categories()
    {
        return $this->belongsToMany(\App\Models\PackageCategory::class);
	}

	/**
     * Splits the given value.
     *
     * @param  string $value
     * @return array
     */
    public function splitTreatmentsQuestionsAndAnswers($value)
    {
        return (array)$value;
	}
	
	public function shouldBeSearchable()
	{
		return $this->published;
	}

	/**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
		$fullArray = $this->toArray();
		
		$fullArray = $this->transform($fullArray);

		// Customize array...
		return [
			'title'                            => $fullArray['title'],
			'treatments_featured_text'         => $fullArray['treatments_featured_text'] ?? '',
			//'treatments_content'               => $fullArray['treatments_content'] ?? '',
			//'treatments_questions_and_answers' => $fullArray['treatments_questions_and_answers'] ?? '',
			'category'                         => $this->category->title ?? ''
		];
    }
    
}
