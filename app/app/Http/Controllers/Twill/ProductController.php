<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;

class ProductController extends ModuleController
{
	protected $moduleName = 'products';

	protected $previewView = 'frontend.productRanges.detail';
	
	/*
     * Options of the index view
     */
    protected $indexOptions = [
        'reorder' => false,
	];
	
	protected $indexColumns = [
		'image' => [
            'thumb' => true, // image column
            'variant' => [
                'role' => 'cover',
                'crop' => 'default',
            ],
        ],
        'title' => [ // field column
            'title' => 'Title',
            'field' => 'title',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
		],
		'range' => [ // relation column
            'title' => 'Range',
            'sort' => true,
            'relationship' => 'range',
            'field' => 'title'
        ],
	];

	/*
     * Can be used in child classes to disable the content editor (full screen block editor)
     */
	protected $disableEditor = true;

	/*
     * Filters mapping ('filterName' => 'filterColumn')
     * You can associate items list to filters by having a filterNameList key in the indexData array
     * For example, 'category' => 'category_id' and 'categoryList' => app(CategoryRepository::class)->listAll()
     */
    protected $filters = [
		'range' => 'range'
	];

	protected function indexData($request)
    {
		$rangeList = \App\Models\ProductRange::all()->map(function($item) {
			return [
				'value' => $item->id,
				'label' => $item->title
			];
		})->toArray();
		
        return [
			'rangeList' => $rangeList
		];
	}

	/*
     * Add anything you would like to have available in your module's form view
     * For example, relationship lists for multiselect form fields
     */
    protected function formData($request)
    {
		$product_ranges = \App\Models\ProductRange::all()->map(function($item) {
			return [
				'value' => $item->id,
				'label' => $item->title
			];
		})->toArray();		

        return ['product_ranges' => $product_ranges];
	}

	protected function previewData($item)
	{
		$product_ranges = \App\Models\ProductRange::all();
		
		return [
			'content' => $item->range,
			'product_ranges' => $product_ranges
		];
	}
}
