<?php

namespace App\Http\Controllers\Frontend;

use App\Mail\CareerApplicationNotification;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use A17\Twill\Repositories\SettingRepository;
use App\Http\Requests\SaveCareerApplicationForm;
use App\Models\CareerApplication;
use Illuminate\Support\Str;

class CareerApplicationController extends Controller
{
    public function store(SaveCareerApplicationForm $request)
    {
		// Generate custom filename using name and date
        $sanitizedName = Str::slug($request->name . '-' . $request->surname);
        $dateString = now()->format('Y-m-d');
        $extension = $request->file('cv')->getClientOriginalExtension();
        $filename = "{$sanitizedName}-{$dateString}.{$extension}";
        
        // Handle the PDF upload with custom filename
        $cvPath = $request->file('cv')->storeAs(
            'uploads/cv_documents',
            $filename,
			'local'
        );

        $cvUrl = Storage::url($cvPath);

        // Create the job application record
        $application = CareerApplication::create([
			'title' => $request->name . ' ' . $request->surname,
            'name' => $request->name,
            'surname' => $request->surname,
            'email' => $request->email,
            'cellphone' => $request->cellphone,
            'region' => $request->region,
            'position' => $request->position,
            'cv_url' => $cvUrl,
            'published' => 1
        ]);

        //Admin notification email
        $notification_email = '<EMAIL>';
		Mail::to($notification_email)->send(new CareerApplicationNotification($application, storage_path('app/' . $cvPath)));

        $redirect = $request->input('redirect') ?? '/';
        return redirect($redirect);
    }
}
