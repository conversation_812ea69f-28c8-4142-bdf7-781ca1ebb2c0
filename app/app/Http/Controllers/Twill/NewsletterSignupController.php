<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;

class NewsletterSignupController extends ModuleController
{
	protected $moduleName = 'newsletterSignups';
	
	/*
     * Can be used in child classes to disable the content editor (full screen block editor)
     */
    protected $disableEditor = true;
	
	/*
     * Options of the index view
     */
    protected $indexOptions = [
        'create' => false,
        'edit' => false,
        // 'publish' => false,
        // 'bulkPublish' => false,
        // 'feature' => false,
        // 'bulkFeature' => false,
        // 'restore' => true,
        // 'bulkRestore' => true,
        'delete' => true,
        // 'bulkDelete' => true,
        // 'reorder' => false,
        // 'permalink' => false,
        // 'bulkEdit' => false,
        // 'editInModal' => false,
	];
	
	/*
     * Available columns of the index view
     */
    protected $indexColumns = [

        'email' => [
            'title' => 'Email',
            'field' => 'email',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
		],
        'emma_id' => [ // presenter column
            'title' => 'Emma Member ID',
            'field' => 'member_id',
            'present' => true,
            'visible' => true,
		],
        'created_at' => [ // presenter column
            'title' => 'Date',
            'field' => 'date',
            'present' => true,
            'visible' => true,
        ]
	];

	protected $titleColumnKey = 'email';
}
