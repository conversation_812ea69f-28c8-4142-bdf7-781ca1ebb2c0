<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;

class RequestBrochureController extends ModuleController
{
	protected $moduleName = 'requestBrochures';
	
	/*
     * Options of the index view
     */
    protected $indexOptions = [
        'create' => false,
        'edit' => false,
        // 'publish' => false,
        // 'bulkPublish' => false,
        // 'feature' => false,
        // 'bulkFeature' => false,
        // 'restore' => true,
        // 'bulkRestore' => true,
        'delete' => true,
        // 'bulkDelete' => true,
        // 'reorder' => false,
        // 'permalink' => false,
        // 'bulkEdit' => false,
        // 'editInModal' => false,
	];

	/*
     * Key of the index column to use as title/name/anythingelse column
     * This will be the first column in the listing and will have a link to the form
     */
    protected $titleColumnKey = 'name';

	/*
     * Available columns of the index view
     */
    protected $indexColumns = [
        'name' => [ // field column
            'title' => 'Name',
			'field' => 'name',
			'sort'  => true
        ],
        'email' => [
            'title' => 'Email',
            'field' => 'email',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
		],
		'phone_number' => [
            'title' => 'Phone Numner',
            'field' => 'phone_number',
            'sort' => false, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'branch' => [ // relation column
            'title' => 'Closest Branch',
            'sort' => true,
            'relationship' => 'branch',
			'field' => 'title',
			'visible' => true,
		],
        'created_at' => [ // presenter column
            'title' => 'Date',
            'field' => 'date',
            'present' => true,
            'visible' => true,
        ],
		'brochures' => [
            'title' => 'Brochures',
            'field' => 'listBrochures',
            'present' => true,
        ],
	];
}
