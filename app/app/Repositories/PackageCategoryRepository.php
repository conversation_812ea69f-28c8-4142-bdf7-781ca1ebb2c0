<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleBlocks;
use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleFiles;
//use A17\Twill\Repositories\Behaviors\HandleRevisions;
use App\Repositories\GlobalRepository;
use App\Models\PackageCategory;

class PackageCategoryRepository extends GlobalRepository
{
    use HandleBlocks, HandleSlugs, HandleMedias, HandleFiles;

    public function __construct(PackageCategory $model)
    {
        $this->model = $model;
    }
}
