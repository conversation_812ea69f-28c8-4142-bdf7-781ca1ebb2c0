<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Condition;
use App\Models\MinkiPost;
use App\Models\Treatment;
use App\Models\Page;
use App\Models\Tip;
use App\Models\ProductRange;
use App\Models\StaffMember;
use App\Models\StaffMemberType;
use App\Models\Mooimaak;
use App\Models\Package;
use App\Models\PackageCategory;




class SitemapController extends Controller
{
    public function html_sitemap()
    {
        $sitemap_sections['Branches'] = [
            'data' => Branch::sitemap_entries(),
            'prefix' => ''
        ];
        $sitemap_sections['Conditions'] = [
            'data' => Condition::sitemap_entries(),
            'prefix' => ''
        ];
        $sitemap_sections['Treatments'] = [
            'data' => Treatment::sitemap_entries(),
            'prefix' => ''
        ];
        $sitemap_sections['Pages'] = [
            'data' => Page::sitemap_entries(),
            'prefix' => ''
        ];
        $sitemap_sections['Tips'] = [
            'data' => Tip::sitemap_entries(),
            'prefix' => config('renewal.general.tips_prefix') . '-tips'
        ];
        $sitemap_sections['Product Ranges'] = [
            'data' => ProductRange::sitemap_entries(),
            'prefix' => ''
        ];
        $sitemap_sections['Staff Members'] = [
            'data' => StaffMember::sitemap_entries(),
            'prefix' => ''
        ];
        $sitemap_sections['Staff Member Types'] = [
            'data' => StaffMemberType::sitemap_entries(),
            'prefix' => 'staff'
        ];
        $sitemap_sections['Mooimaak'] = [
            'data' => Mooimaak::sitemap_entries(),
            'prefix' => 'mooimaak'
        ];
        $sitemap_sections['Minki'] = [
            'data' => MinkiPost::sitemap_entries(),
            'prefix' => 'minki'
        ];
        $sitemap_sections['Packages'] = [
            'data' => Package::sitemap_entries(),
            'prefix' => 'package'
        ];

        $page = Page::forSlug('sitemap')->first();

        if (!$page) {
            $page = new \stdClass;
            $seo_meta = new \App\Models\SeoMeta;
            $seo_meta->page_title =  'Sitemap for ' . env('APP_NAME', 'Skin Renewal');
            $seo_meta->page_description = 'Cant find what you’re looking for? Review the Site Map for ' . env('APP_NAME', 'Skin Renewal') . ' Websites for a list of our pages on treatments and services available in Johannesburg, Pretoria, Cape Town and KwaZulu Natal.';
            $page->seo_meta = $seo_meta;
        }


        return view('frontend.sitemap.index-html', [
            'page' => $page,
            'sitemap_sections' => $sitemap_sections,
        ]);
    }

    public function index()
    {
        $branch = Branch::published()->visible()->orderBy('updated_at', 'desc')->first();
        $condition = Condition::published()->visible()->orderBy('updated_at', 'desc')->first();
        $treatment = Treatment::published()->visible()->orderBy('updated_at', 'desc')->first();
        $product_range = ProductRange::published()->visible()->orderBy('updated_at', 'desc')->first();
        $staff = StaffMember::published()->visible()->orderBy('updated_at', 'desc')->first();
        $staff_type = StaffMemberType::published()->visible()->orderBy('updated_at', 'desc')->first();
        $tip = Tip::published()->visible()->orderBy('updated_at', 'desc')->first();
        $page = Page::published()->visible()->orderBy('updated_at', 'desc')->first();
        $mooimaak = Mooimaak::published()->visible()->orderBy('updated_at', 'desc')->first();
        $minki = MinkiPost::published()->visible()->orderBy('updated_at', 'desc')->first();
        $pricelist = PackageCategory::published()->visible()->orderBy('updated_at', 'desc')->first();
        $packages = Package::published()->visible()->orderBy('updated_at', 'desc')->first();

        return response()->view('frontend.sitemap.index-xml', [
            'branch'        => $branch,
            'condition'     => $condition,
            'treatment'     => $treatment,
            'product_range' => $product_range,
            'staff'         => $staff,
            'staff_type'    => $staff_type,
            'tip'           => $tip,
            'page'          => $page,
            'mooimaak'      => $mooimaak,
            'minki'         => $minki,
            'pricelist'     => $pricelist,
            'packages'      => $packages,
        ])->header('Content-Type', 'text/xml');
    }

    public function branches()
    {
        $urls = Branch::sitemap_entries();

        return response()->view('frontend.sitemap.default-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function conditions()
    {
        $urls = Condition::sitemap_entries();

        return response()->view('frontend.sitemap.default-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function treatments()
    {
        $urls = Treatment::sitemap_entries();

        return response()->view('frontend.sitemap.default-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function product_ranges()
    {
        $urls = ProductRange::sitemap_entries();

        return response()->view('frontend.sitemap.default-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function staff()
    {
        $urls = StaffMember::sitemap_entries();

        return response()->view('frontend.sitemap.staff-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function staff_types()
    {
        $urls = StaffMemberType::sitemap_entries();

        return response()->view('frontend.sitemap.staff-types-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function tips()
    {
        $urls = Tip::sitemap_entries();

        return response()->view('frontend.sitemap.tips-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function pages()
    {
        $urls = Page::sitemap_entries();

        return response()->view('frontend.sitemap.pages-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function mooimaak()
    {
        $urls = Mooimaak::sitemap_entries();

        return response()->view('frontend.sitemap.mooimaak-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function minki()
    {
        $urls = MinkiPost::sitemap_entries();

        return response()->view('frontend.sitemap.minki-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function packages()
    {
        $urls = Package::sitemap_entries();

        return response()->view('frontend.sitemap.packages-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function pricelists()
    {
        $urls = PackageCategory::sitemap_entries();

        return response()->view('frontend.sitemap.pricelists-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function sections()
    {
        $urls = Page::published()->visible()->where(function ($query) {
            $query->where('template', 'section-silo-products')
                ->orWhere('template', 'section-silo-packages')
                ->orwhere('template', 'section-silo');
        })->get();

        return response()->view('frontend.sitemap.sections-xml', [
            'urls' => $urls,
        ])->header('Content-Type', 'text/xml');
    }

    public function clear_cache()
    {
        $exitCode = \Artisan::call('optimize:clear');
        $exitCode2 = \ResponseCache::clear();
        session()->flash('status', 'Cache Cleared');
        return redirect()->back();
    }
}
