<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Intervention\Image\Facades\Image;

class BotoxFormController extends Controller
{
    public function save_image(Request $request) {
		// $file = base64_decode($request['botox-image']);
        // $folderName = 'public/uploads/botox-images';
        // $safeName = 'botox-image-'. time() . '.png';
        // $destinationPath = public_path() . $folderName;
        // $success = file_put_contents(public_path().'/uploads/'.$safeName, $file);
		// print $success;
		
		// $data = Input::all();
		$filename = "botox-image-".time().".png";
		$path = public_path(). '/uploads/botox-images/' . $filename;
		// dd($path);
		$result = Image::make(file_get_contents($request['botox-image']))->save($path);     
		$response = array(
			'status' => url('/uploads/botox-images/' . $filename),
		);
		return $response;
	}
}
