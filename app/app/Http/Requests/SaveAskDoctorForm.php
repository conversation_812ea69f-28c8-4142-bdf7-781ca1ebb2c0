<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SaveAskDoctorForm extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
			'name'                 => 'required',
			'surname'              => 'required',
			'email'                => 'required|email',
			'phone_number'         => 'required',
			'how_did_you_hear'     => 'required',
			'branch_id'            => 'required',
			'questions'            => 'required',
			'g-recaptcha-response' => 'recaptcha',
        ];
    }
}
