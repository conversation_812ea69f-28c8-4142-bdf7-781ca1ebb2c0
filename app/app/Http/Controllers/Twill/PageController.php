<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;
use Config;

class PageController extends ModuleController
{
	protected $moduleName = 'pages';
	
	protected $perPage = 20;

	protected $permalinkBase = '';
	
	/*
     * Add anything you would like to have available in your module's form view
     * For example, relationship lists for multiselect form fields
     */
    protected function formData($request)
    {
		$template_base_path = Config::get('view.paths');
		$template_path = $template_base_path[0] . '/frontend/pages';
		
		//Get a list of template names from the pages template folder
		// $templates = glob($template_path.'/*.blade.php');
		$templates = array_filter(glob($template_path.'/*.blade.php'), function($v) {
			return false === strpos($v, '-amp');
		});
		
		//Format for twills select field
		$templates = array_map(function ($item) {
			$name = basename($item, '.blade.php');
			return [
				'value' => $name,
				'label' => $name
			]; 
		}, array_values($templates));

		//Get a list of form template names from the forms template folder
		$form_template_path = $template_base_path[0] . '/frontend/forms';
		$form_templates = array_filter(glob($form_template_path.'/*.blade.php'), function($v) {
			return false === strpos($v, '-amp');
		});

		//Format for twills select field
		$form_templates = array_map(function ($item) {
			$name = basename($item, '.blade.php');
			return [
				'value' => $name,
				'label' => $name
			]; 
		}, array_values($form_templates));

		return [
			'templates'      => $templates, 
			'form_templates' => $form_templates
		];
	}

	protected $indexColumns = [
        'title' => [ // field column
            'title' => 'Title',
            'field' => 'title',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'slug' => [
            'title' => 'Slug',
            'field' => 'slug',
            'visible' => true, // will be available from the columns settings dropdown
		],
        'tempalte' => [
            'title' => 'Template',
            'field' => 'template',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ]
	];

	/*
     * Filters mapping ('filterName' => 'filterColumn')
     * You can associate items list to filters by having a filterNameList key in the indexData array
     * For example, 'category' => 'category_id' and 'categoryList' => app(CategoryRepository::class)->listAll()
     */
    protected $filters = [
		'template' => 'template'
	];
	
	protected function indexData($request)
    {
		$templateList = \App\Models\Page::distinct()->pluck('template')->map(function($item, $key) {
			return [
				'value' => $item,
				'label' => $item
			];
		})->toArray();
		
        return [
			'templateList' => $templateList
		];
	}

	public function preview($id): \Illuminate\Contracts\View\View
    {
        if (request()->has('revisionId')) {
            $item = $this->repository->previewForRevision($id, request('revisionId'));
        } else {
            $formRequest = $this->validateFormRequest();
            $item = $this->repository->preview($id, $formRequest->all());
        }

        if (request()->has('activeLanguage')) {
            $this->app->setLocale(request('activeLanguage'));
		}

		$previewView = 'frontend.pages.' . $item->template;

		if($item->template == 'section-silo' || $item->template == 'section-silo-products' || $item->template == 'section-silo-packages') {
			switch ($item->slug) {
				case 'conditions':
					$unformatted_link_list = \App\Models\ConditionCategory::published()->visible()
						->orderBy('position', 'asc')
						->with('conditions')
						->with(['conditions.slugs' => function ($query) {
							$query->where('active', true);
						}])
						->get()
						->toArray();
	
					foreach($unformatted_link_list as $link_block) {
						foreach($link_block['conditions'] as $index => $condition) {
							if ($condition['published'] == 0) {
								unset($link_block['conditions'][$index]);
							}
						}
						$link_list[$link_block['title']] = $link_block['conditions'];
					}
					break;
				case 'treatments':
					$unformatted_link_list = \App\Models\TreatmentCategory::published()->visible()
						->orderBy('position', 'asc')
						->with('treatments')
						->with(['treatments.slugs' => function ($query) {
							$query->where('active', true);
						}])
						->get()
						->toArray();
	
					foreach($unformatted_link_list as $link_block) {
						foreach($link_block['treatments'] as $index => $treatment) {
							if ($treatment['published'] == 0) {
								unset($link_block['treatments'][$index]);
							}
						}
						$link_list[$link_block['title']] = $link_block['treatments'];
					}
					break;
				case 'branches':
					$link_list = \App\Models\Branch::published()->visible()
						->orderBy('province', 'asc')
						->with(['slugs' => function ($query) {
							$query->where('active', true);
						}])
						->get()
						->groupBy('province');
					
					//Sort branch groupings
					foreach($link_list as $index => $groups) {
						$link_list[$index] = $groups->sortBy('title');
					}
	
					$link_list = $link_list->toArray();
					break;
				case 'products':;
					$link_list = \App\Models\ProductRange::published()->visible()->orderBy('position', 'asc')->with(['slugs' => function ($query) {
						$query->where('active', true);
					}])->get();
					break;
				case 'prices':;
					$data['package_categories'] = \App\Models\PackageCategory::orderBy('title', 'ASC')->get();
					break;
				case 'about':
					$link_list = config('renewal.about');
					break;
				default:
					return abort(404);
					break;
			}
			
			$data['page']      = $item; 
			$data['link_list'] = $link_list;
			$data['slug']      = $item->slug;
			return view($previewView, $data);
		}
		
        

        return view()->exists($previewView) ? view($previewView, array_replace([
            'item' => $item,
        ], $this->previewData($item))) : view('twill::errors.preview', [
            'moduleName' => str_singular($this->moduleName),
        ]);
    }

	protected function previewData($item)
	{
		return [
			'page' => $item
		];
	}
}
