<?php

namespace App\Models\Presenters;

use Illuminate\Support\Facades\Log;
use App\Models\Presenters\BasePresenter;

class FormsPresenter extends BasePresenter
{
    protected $entity; // This is to store the original model instance

    public function date()
    {
        return $this->entity->created_at->format('Y-m-d');
    }

    public function listBrochures()
    {
        if ($this->entity->brochures == null) {
            return '';
        }

        $brochures = '<ul>';
        foreach ($this->entity->brochures as $brochure) {
            $brochure = explode('|', $brochure);
            $brochures .= '<li>' . $brochure[0] . '</li>';
        }
        $brochures .= '</ul>';
        return $brochures;
    }

    public function member_id()
    {
        Log::info('Entity data:', ['data' => $this->entity->data]);
        if ($this->entity->data == null) {
            return '';
        }

        return $this->entity->data['signup_response'];
    }
}
