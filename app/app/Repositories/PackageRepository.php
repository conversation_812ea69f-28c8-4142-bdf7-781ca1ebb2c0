<?php

namespace App\Repositories;

use A17\Twill\Repositories\Behaviors\HandleBlocks;
use A17\Twill\Repositories\Behaviors\HandleSlugs;
use A17\Twill\Repositories\Behaviors\HandleMedias;
use A17\Twill\Repositories\Behaviors\HandleFiles;
use A17\Twill\Repositories\Behaviors\HandleRevisions;
use App\Repositories\GlobalRepository;
use App\Models\Package;

class PackageRepository extends GlobalRepository
{
    use HandleBlocks, HandleSlugs, HandleMedias, HandleFiles, HandleRevisions;

    public function __construct(Package $model)
    {
        $this->model = $model;
	}
	
	// implement the getFormFields method
	public function getFormFields(\A17\Twill\Models\Contracts\TwillModelContract $object): array {
		// don't forget to call the parent getFormFields function
		$fields = parent::getFormFields($object);

		//Q and A repeater
		if (isset($fields['external_related_treatments'])) {
			$fields = $this->getJsonRepeater($fields, 'external_related_treatment', 'external_related_treatments');
		}
		
		// return fields
		return $fields;
	}
	
	public function prepareFieldsBeforeSave(\A17\Twill\Models\Contracts\TwillModelContract $object, array $fields): array
    {

        if (isset($fields['repeaters']['external_related_treatment'])) {
			$fields['external_related_treatments'] = $fields['repeaters']['external_related_treatment'];
		}

        return parent::prepareFieldsBeforeSave($object, $fields);
	}

	// implement the filter method
	public function filter(\Illuminate\Database\Eloquent\Builder $query, array $scopes = []): \Illuminate\Database\Eloquent\Builder {

		if (isset($scopes['category'])) {
			$query->whereHas('category', function ($query) use ($scopes) {
				$query->where('id', $scopes['category']);
			});
			unset($scopes['category']);
		}

		return parent::filter($query, $scopes);
	}
}
