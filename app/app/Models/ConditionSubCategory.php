<?php

namespace App\Models;

use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Behaviors\Sortable;
use A17\Twill\Models\Model;

class ConditionSubCategory extends Model implements Sortable
{
    use HasPosition;

    protected $fillable = [
        'published',
        'title',
        'description',
        'position',
		'condition_category_id'
    ];

	public function parent_category() {
		return $this->belongsTo(ConditionCategory::class, 'condition_category_id');
	}
    
	public function conditions() {
		return $this->hasMany(Condition::class);
	}
}
