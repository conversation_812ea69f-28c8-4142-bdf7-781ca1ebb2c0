<?php
namespace App\Services;

use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Client;

// class Continuon {

// 	function __construct()
// 	{
// 		$this->continuon_base_url = "https://skinbodyrenewal.api.continuon.co/api/v1/";
// 	}

// 	/**
//      * Submit data to continuon
//      *
//      *
//      * <AUTHOR> <<EMAIL>>
//      * @return string JSON  true
//      */
// 	function submit($data) {
// 		$result = false;
//         $continuon_data = [];
//         $group = [
//         	'4461250' => 'Body Renewal Website',
//         	'4463298' => 'Brain & Sleep Renewal Website',
//         	'4462274' => 'Health Renewal Website',
//         	'4459202' => 'Skin Renewal Website'
//         ];

//         $interest = [];
//         $i=0;
//         foreach ($data['group'] as $index => $val) {
// 			$interest[$i] = (isset($group[$index]))? $group[$index]: '';
// 			$i++;
// 		}

//         $continuon_data['interest'] = $interest;

//         $continuon_data['banner_id'] = (isset($data['continuon_banner_id']))? $data['continuon_banner_id']: '';
//         $continuon_data['reference'] = (isset($data['continuon_reference']))? $data['continuon_reference']: '';

//         if (!empty($continuon_data['banner_id']) || !empty($continuon_data['banner_id']) ) {
//         	$continuon_data['email'] = (isset($data['email']))? $data['email']: '';
// 	        $fields= (isset($data['fields']))? $data['fields']: [];

// 	        if (is_array($fields) && count($fields) > 0) {
// 	        	$continuon_data['first_name'] = (isset($fields['first_name']))? $fields['first_name']: '';
// 	        	$continuon_data['last_name'] = (isset($fields['last_name']))? $fields['last_name']: '';
// 	        	$continuon_data['city'] = (isset($fields['preferred-branch']))? $fields['preferred-branch']: '';
// 	        	$continuon_data['brand'] = 'SKINBODYRENEWAL';
// 	        }

// 	        $client = new Client([
// 	            'base_uri' => $this->continuon_base_url,
// 	        ]);
// 	        $res = $client->post('social-plugin/save', ['form_params' => $continuon_data]);

// 	        $contents = $res->getBody()->getContents();
// 	        $result = json_decode($contents);
//         }

//         return $result;
// 	}
// }