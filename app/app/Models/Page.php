<?php

namespace App\Models;

//use A17\Twill\Models\Behaviors\HasBlocks;
use A17\Twill\Models\Model;
use <PERSON>vel\Scout\Searchable;
use App\Models\Behaviors\HasSeo;
use App\Models\Behaviors\HasMyBlocks;
use Illuminate\Support\Facades\Cache;
//use A17\Twill\Models\Behaviors\Sortable;
use A17\Twill\Models\Behaviors\HasSlug;
use A17\Twill\Models\Behaviors\HasFiles;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Behaviors\HasRevisions;

class Page extends Model
{
    use HasMyBlocks, HasSlug, HasMedias, HasFiles, HasRevisions, HasPosition, HasSeo, Searchable;

    protected $fillable = [
        'published',
        'title',
        'content',
        'position',
        'template',
        'form_template',
        'publish_start_date',
        'publish_end_date',
        'recipes'
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = [];

    // uncomment and modify this as needed if you use the HasSlug trait
    public $slugAttributes = [
        'title',
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
    ];

    // uncomment and modify this as needed if you use the HasMedias trait
    public $mediasParams = [
        'content_image' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ],
            'mobile' => [
                [
                    'name' => 'mobile',
                    'ratio' => 1,
                ],
            ],
        ],
    ];

    public $filesParams = ['file_role']; // a list of file roles

    public function slug()
    {
        return $this->hasOne('App\Models\Slugs\PageSlug');
    }

    /**
     * Splits the given value.
     *
     * @param  string $value
     * @return array
     */
    public function splitContent($value)
    {
        return explode('. ', $value);
    }

    public function shouldBeSearchable()
    {
        return $this->published;
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $fullArray = $this->toArray();

        $fullArray = $this->transform($fullArray);

        // Customize array...
        $data =  [
            'title'   => $fullArray['title'],
        ];

        // if (isset($fullArray['content'])) {
        // 	$data['content'] = $fullArray['content'];
        // }

        return $data;
    }

    /**
     * Clear cache when data is saved or deleted.
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            Cache::forget('memberships_data');
        });

        static::deleted(function () {
            Cache::forget('memberships_data');
        });
    }
}
