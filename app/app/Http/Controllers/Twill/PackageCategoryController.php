<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;

class PackageCategoryController extends ModuleController
{
	protected $moduleName = 'packageCategories';

	/*
     * Can be used in child classes to disable the content editor (full screen block editor)
     */
	protected $disableEditor = true;
	
	protected $permalinkBase = 'pricelists';
	
	/*
     * Options of the index view
     */
    protected $indexOptions = [
		'permalink' => true,
		'reorder' => true,
    ];
	
	protected $indexColumns = [
        'image' => [
            'thumb' => true, // image column
            'variant' => [
                'role' => 'cover',
                'crop' => 'default',
            ],
        ],
        'title' => [ // field column
            'title' => 'Title',
            'field' => 'title',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'slug' => [
            'title' => 'Slug',
            'field' => 'slug',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ]
    ];
}
