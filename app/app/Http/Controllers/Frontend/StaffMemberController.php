<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\StaffMember;
use App\Models\StaffMemberType;
use stdClass;

class StaffMemberController extends Controller
{
    public function index($slug) {
		$content = StaffMemberType::forSlug($slug)->published()->visible()->with(['staff' => function ($query) {
			$query->published()->visible();
		}])->first();

		if (!$content) {
			return abort(404);
		}

		$staff_member_types = StaffMemberType::published()->visible()->get();

		return view('frontend.staffMembers.index', [
			'content'            => $content,
			'staff_member_types' => $staff_member_types
		]);
	}

	public function detail($type, $slug) {
		$content = StaffMember::forSlug($slug)->published()->visible()->first();

		if (!$content) {
			return abort(404);
		}

		$staff_member_type = StaffMemberType::forSlug($type)->with(['staff' => function ($query) {
			$query->published()->visible()->limit(6);
		}])->first();

		return view('frontend.staffMembers.detail', [
			'content' 			=> $content,
			'staff_member_type' => $staff_member_type
		]);
	}
}
