<?php

namespace App\Models;

use <PERSON><PERSON>\Scout\Searchable;
use A17\Twill\Models\Behaviors\HasBlocks;
use A17\Twill\Models\Behaviors\HasSlug;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasRevisions;
use App\Models\Behaviors\HasSeo;
use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Model;
use App\Models\Behaviors\HasMyBlocks;

class Branch extends Model
{

    use HasBlocks, HasSlug, HasMedias, HasPosition, HasRevisions, HasSeo, Searchable, HasMyBlocks;

    protected $fillable = [
        'published',
        'title',
        'branch_intro',
        'address',
        'phone_number',
        'mobile_number',
        'whatsapp_number',
        'fax_number',
        'branch_email',
        'monday_friday',
        'saturday',
        'sunday',
        'google_map',
        'facebook_url',
        'branch_gallery',
        'instagram_page',
        'branch_3d_video',
        'branch_manager_id',
        'province',
        'position',
        'publish_start_date',
        'publish_end_date',
        'google_business_page',
        'packages',
        'latitude',
        'longitude',
        'branch_newsletter_name',
        'branch_newsletter_link',
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = [];

    // uncomment and modify this as needed if you use the HasSlug trait
    public $slugAttributes = [
        'title'
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
    ];

    //Return JSON data as an array
    protected $casts = [
        'address' => 'array',
        'branch_gallery' => 'array',
        'packages' => 'array',
    ];

    public function treatments()
    {
        return $this->belongsToMany(\App\Models\Treatment::class);
    }

    public function staff()
    {
        return $this->belongsToMany(\App\Models\StaffMember::class);
    }

    public function product_ranges()
    {
        return $this->belongsToMany(\App\Models\ProductRange::class);
    }

    public function package_categories()
    {
        return $this->belongsToMany(\App\Models\PackageCategory::class);
    }

    public function manager()
    {
        return $this->belongsTo(\App\Models\StaffMember::class, 'branch_manager_id');
    }

    public function reviews()
    {
        return $this->hasMany(\App\Models\Review::class);
    }

    // uncomment and modify this as needed if you use the HasMedias trait
    public $mediasParams = [
        'slideshow' => [
            'default' => [
                [
                    'name' => 'landscape',
                    'ratio' => 16 / 9,
                ],
                [
                    'name' => 'portrait',
                    'ratio' => 3 / 4,
                ],
            ],
            'mobile' => [
                [
                    'name' => 'mobile',
                    'ratio' => 1,
                ],
            ],
        ],
        'whatsapp_qr' => [
            'desktop' => [
                [
                    'name' => 'desktop',
                    'ratio' => 1 / 1,
                    'minValues' => [
                        'width' => 100,
                        'height' => 100,
                    ],
                ],
            ]
        ]
    ];

    public function shouldBeSearchable()
    {
        return $this->published;
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        $fullArray = $this->toArray();

        $fullArray = $this->transform($fullArray);

        // Customize array...
        return [
            'title'         => $fullArray['title'],
            // 'branch_intro'  => $fullArray['branch_intro'] ?? '',
            // 'address'       => $fullArray['address'] ?? '',
            // 'phone_number'  => $fullArray['phone_number'] ?? '',
            // 'mobile_number' => $fullArray['mobile_number'] ?? '',
            // 'branch_email'  => $fullArray['branch_email'] ?? '',
            'province'      => $fullArray['province'] ?? '',
        ];
    }
}
