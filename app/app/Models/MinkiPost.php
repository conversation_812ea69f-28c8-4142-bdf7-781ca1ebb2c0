<?php

namespace App\Models;

use App\Models\Behaviors\HasMyBlocks;
use A17\Twill\Models\Behaviors\HasSlug;
use A17\Twill\Models\Behaviors\HasMedias;
use A17\Twill\Models\Behaviors\HasFiles;
use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Behaviors\Sortable;
use A17\Twill\Models\Model;
use App\Models\Behaviors\HasSeo;

class MinkiPost extends Model implements Sortable
{
    use HasMyBlocks, HasSlug, HasMedias, HasFiles, HasPosition, HasSeo;

    protected $fillable = [
        'title',
        'excerpt',
        'first_name',
        'last_name',
        'season',
        'about_contestant',
        'main_concerns',
        'treatment_plan',
        'products_used',
        'seo_meta_id',
        'publish_start_date',
        'publish_end_date',
        'position',
        'published'
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = [];

    public $slugAttributes = [
        'title',
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
    ];

    // uncomment and modify this as needed if you use the HasMedias trait
    public $mediasParams = [
        'cover' => [
            'default' => [
                [
                    'name' => 'default',
                    'ratio' => 1,
                ],
            ],
        ],
        'listing_before_pic' => [
            'default' => [
                [
                    'name' => 'default',
                    'ratio' => 1,
                ],
            ]
        ],
        'listing_after_pic' => [
            'default' => [
                [
                    'name' => 'default',
                    'ratio' => 1,
                ],
            ]
        ],
        'before_after' => [
            'default' => [
                [
                    'name' => 'default',
                    'ratio' => 1,
                ],
            ],
        ],
    ];
}
