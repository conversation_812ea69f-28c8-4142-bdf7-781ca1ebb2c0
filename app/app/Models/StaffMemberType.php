<?php

namespace App\Models;

use A17\Twill\Models\Behaviors\HasSlug;
use A17\Twill\Models\Behaviors\HasMedias;
//use A17\Twill\Models\Behaviors\HasRevisions;
use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Behaviors\Sortable;
use A17\Twill\Models\Model;
use App\Models\Behaviors\HasSeo;

class StaffMemberType extends Model implements Sortable
{
    use HasSlug, HasMedias, HasPosition, HasSeo;

    protected $fillable = [
        'published',
        'title',
		'description',
		'sub_content',
		'seo_meta_id',
        'position',
        'publish_start_date',
        'publish_end_date',
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = [];

    // uncomment and modify this as needed if you use the HasSlug trait
    public $slugAttributes = [
        'title',
    ];

    // add checkbox fields names here (published toggle is itself a checkbox)
    public $checkboxes = [
        'published'
    ];

	public function staff()
    {
        return $this->hasMany(\App\Models\StaffMember::class, 'staff_members_type_id')->orderBy('position');
	}
}
