<?php

namespace App\Models;

use A17\Twill\Models\Behaviors\HasPosition;
use A17\Twill\Models\Behaviors\Sortable;
use A17\Twill\Models\Model;

class TreatmentSubCategory extends Model implements Sortable
{
    use HasPosition;

    protected $fillable = [
        'published',
        'title',
        'description',
        'position',
        'treatment_category_id'
    ];

    public function parent_category()
    {
        return $this->belongsTo(TreatmentCategory::class, 'treatment_category_id');
    }

    public function treatments()
    {
        return $this->hasMany(Treatment::class);
    }
}
