<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Package;
use App\Models\PackageCategory;

class PackageController extends Controller
{
    public function index($slug) {
		$package_categories = PackageCategory::orderBy('position', 'ASC')->published()->get();
		$content = PackageCategory::forSlug($slug)->with(['packages' => function($query) {
			$query->published()->orderBy('position', 'ASC');
		}])->first();

		if ($content == null) {
			return abort(404);
		}

		return view('frontend.packageCategories.detail', [
			'content' => $content,
			'package_categories' => $package_categories
		]);
	}

	public function detail($slug) {
		$content = Package::forSlug($slug)->published()->first();

		if ($content == null) {
			return abort(404);
		}

		$siblings = Package::where('package_category_id', $content->package_category_id)->orderBy('position', 'ASC')->published()->get();
		$next = Package::where('position', '>', $content->position)->where('package_category_id', $content->package_category_id)->orderBy('position', 'ASC')->published()->first();
		$previous = Package::where('position', '<', $content->position)->where('package_category_id', $content->package_category_id)->orderBy('position', 'DESC')->published()->first();

        // dd($previous);
		// dd($siblings);

		return view('frontend.packages.detail', [
			'content' => $content,
			'next' => $next,
			'previous' => $previous,
		]);
	}
}
