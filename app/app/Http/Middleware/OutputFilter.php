<?php

namespace App\Http\Middleware;

use Closure;

class OutputFilter
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);
		$output = $response->getOriginalContent();
		//dd($output);
		if(is_object($output)) {
			$data = $output->getData();

			//Ensure all urls are https
			//$output = str_replace('http:', 'https:', $output);

			//Temporary filter for stagins
			if (\App::environment('staging')) {
				$output = str_replace('sleeprenewal.co.za', 'sleep-renewal.platinumseed.com', $output);
				$output = str_replace('www.sleep-renewal.platinumseed.com', 'sleep-renewal.platinumseed.com', $output);
			}
			
			$response->setContent($output);
		}
		
        return $response;
    }
}
