<?php

namespace App\Http\Controllers\Twill;

use A17\Twill\Http\Controllers\Admin\ModuleController;

class ConditionController extends ModuleController
{
    protected $moduleName = 'conditions';

    protected $permalinkBase = '';

    /*
     * Can be used in child classes to disable the content editor (full screen block editor)
     */
    protected $disableEditor = true;

    protected $previewView = 'frontend.conditions.detail';

    protected $indexColumns = [
        'title' => [ // field column
            'title' => 'Title',
            'field' => 'title',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'slug' => [
            'title' => 'Slug',
            'field' => 'slug',
            'sort' => true, // column is sortable
            'visible' => true, // will be available from the columns settings dropdown
        ],
        'category' => [ // relation column
            'title' => 'Category',
            'sort' => true,
            'relationship' => 'category',
            'field' => 'title'
        ],
    ];

    /*
     * Filters mapping ('filterName' => 'filterColumn')
     * You can associate items list to filters by having a filterNameList key in the indexData array
     * For example, 'category' => 'category_id' and 'categoryList' => app(CategoryRepository::class)->listAll()
     */
    protected $filters = [
        'category' => 'category'
    ];

    protected function indexData($request)
    {
        $categoryList = \App\Models\ConditionCategory::all()->map(function ($item) {
            return [
                'value' => $item->id,
                'label' => $item->title
            ];
        })->toArray();

        return [
            'categoryList' => $categoryList
        ];
    }

    /*
     * Add anything you would like to have available in your module's form view
     * For example, relationship lists for multiselect form fields
     */
    protected function formData($request)
    {
        $categories = [];
        $categories_list = \App\Models\ConditionCategory::orderBy('position', 'ASC')->published()->visible()->get();
        foreach ($categories_list as $category) {
            $categories[] = [
                'value' => $category->id,
                'label' => $category->title
            ];
        }

        $sub_categories = [];
        $sub_categories_list = \App\Models\ConditionSubCategory::orderBy('position', 'ASC')->published()->visible()->get();
        foreach ($sub_categories_list as $sub_category) {
            $sub_categories[] = [
                'value' => $sub_category->id,
                'label' => $sub_category->title
            ];
        }

        $package_categories = [];
        $package_categories_list = \App\Models\PackageCategory::all();
        foreach ($package_categories_list as $package_category) {
            $package_categories[] = [
                'value' => $package_category->id,
                'label' => $package_category->title
            ];
        }

        return [
            'categories' => $categories,
            'sub_categories' => $sub_categories,
            'package_categories' => $package_categories
        ];
    }

    protected function previewData($item)
    {
        return [
            'content' => $item
        ];
    }
}
